/**
 * 模板转换工具
 * 将templates.js的模板配置转换为精臣B1的绘制格式
 * B1打印机精度为200dpi，所以1mm=8px
 */

/**
 * 将mm单位转换为px单位（200dpi）
 * @param {number} mm 毫米值
 * @returns {number} 像素值
 */
function mmToPx(mm) {
  return Math.round(mm * 8); // 200dpi: 1mm = 8px
}

/**
 * 将px单位转换为mm单位（200dpi）
 * @param {number} px 像素值
 * @returns {number} 毫米值
 */
function pxToMm(px) {
  return Math.round(px / 8 * 100) / 100; // 保留两位小数
}

/**
 * 转换模板配置为精臣B1绘制参数
 * @param {Object} template 原始模板配置
 * @param {Object} labelContent 标签内容数据
 * @returns {Object} 精臣B1绘制参数
 */
function convertTemplateToJCAPI(template, labelContent = {}) {
  if (!template) {
    throw new Error('模板配置不能为空');
  }

  // 转换基本参数
  const jcParams = {
    // 画布尺寸（转换为像素）
    canvasWidth: mmToPx(template.Width),
    canvasHeight: mmToPx(template.Height),
    
    // 原始尺寸（毫米）
    templateWidth: template.Width,
    templateHeight: template.Height,
    
    // 旋转角度
    rotation: template.Rotate || 0,
    
    // 打印参数
    copies: template.Copies || 1,
    density: template.Density || 3,
    
    // B1不支持gap设置，忽略Gap参数
    // gap: template.Gap,
    
    // 绘制元素列表
    drawElements: []
  };

  // 转换绘制对象
  if (template.DrawObjects && Array.isArray(template.DrawObjects)) {
    template.DrawObjects.forEach((drawObj, index) => {
      const element = convertDrawObject(drawObj, index, labelContent);
      if (element) {
        jcParams.drawElements.push(element);
      }
    });
  }

  return jcParams;
}

/**
 * 转换单个绘制对象
 * @param {Object} drawObj 原始绘制对象
 * @param {number} index 对象索引
 * @param {Object} labelContent 标签内容数据
 * @returns {Object|null} 精臣绘制元素
 */
function convertDrawObject(drawObj, index, labelContent) {
  if (!drawObj) return null;

  // 基本坐标转换（mm转px）
  const baseElement = {
    x: mmToPx(drawObj.X || 0),
    y: mmToPx(drawObj.Y || 0),
    width: mmToPx(drawObj.Width || 0),
    height: mmToPx(drawObj.Height || 0),
    rotation: drawObj.Orientation || 0
  };

  // 根据格式类型处理
  switch (drawObj.Format) {
    case 'TEXT':
      return convertTextElement(drawObj, baseElement, index, labelContent);
    
    case 'BARCODE':
      return convertBarcodeElement(drawObj, baseElement);
    
    case 'QRCODE':
      return convertQRCodeElement(drawObj, baseElement);
    
    case 'LINE':
      return convertLineElement(drawObj, baseElement);
    
    case 'RECTANGLE':
      return convertRectangleElement(drawObj, baseElement);
    
    case 'IMAGE':
      return convertImageElement(drawObj, baseElement);
    
    default:
      // 默认按文本处理
      return convertTextElement(drawObj, baseElement, index, labelContent);
  }
}

/**
 * 转换文本元素
 * @param {Object} drawObj 原始绘制对象
 * @param {Object} baseElement 基本元素信息
 * @param {number} index 元素索引
 * @param {Object} labelContent 标签内容数据
 * @returns {Object} 文本绘制元素
 */
function convertTextElement(drawObj, baseElement, index, labelContent) {
  // 根据索引替换动态内容
  let content = drawObj.Content || '';
  if (labelContent) {
    // 根据模板中的内容模式替换
    if (index === 1 && labelContent.productName !== undefined) {
      content = labelContent.productName; // 品名
    } else if (index === 3 && labelContent.operator !== undefined) {
      content = labelContent.operator; // 操作人
    } else if (index === 5 && labelContent.date !== undefined) {
      content = labelContent.date; // 日期
    }
  }

  return {
    type: 'text',
    x: baseElement.x,
    y: baseElement.y,
    rotation: baseElement.rotation,
    content: content,
    fontSize: parseFloat(drawObj.FontSize) || 4,
    options: {
      family: drawObj.FontName || 'HarmonyOS Sans SC',
      bold: drawObj.FontStyle === 2,
      italic: false,
      align: 'left'
    }
  };
}

/**
 * 转换条码元素
 * @param {Object} drawObj 原始绘制对象
 * @param {Object} baseElement 基本元素信息
 * @returns {Object} 条码绘制元素
 */
function convertBarcodeElement(drawObj, baseElement) {
  return {
    type: 'barcode',
    x: baseElement.x,
    y: baseElement.y,
    width: baseElement.width,
    height: baseElement.height,
    rotation: baseElement.rotation,
    content: drawObj.Content || '12345678',
    fontSize: parseFloat(drawObj.FontSize) || 2.5,
    fontHeight: parseFloat(drawObj.FontHeight) || 3,
    fontPosition: parseInt(drawObj.FontPosition) || 2
  };
}

/**
 * 转换二维码元素
 * @param {Object} drawObj 原始绘制对象
 * @param {Object} baseElement 基本元素信息
 * @returns {Object} 二维码绘制元素
 */
function convertQRCodeElement(drawObj, baseElement) {
  return {
    type: 'qrcode',
    x: baseElement.x,
    y: baseElement.y,
    width: baseElement.width,
    height: baseElement.height,
    rotation: baseElement.rotation,
    content: drawObj.Content || '12345678'
  };
}

/**
 * 转换线条元素
 * @param {Object} drawObj 原始绘制对象
 * @param {Object} baseElement 基本元素信息
 * @returns {Object} 线条绘制元素
 */
function convertLineElement(drawObj, baseElement) {
  return {
    type: 'line',
    x: baseElement.x,
    y: baseElement.y,
    width: baseElement.width,
    height: baseElement.height,
    rotation: baseElement.rotation
  };
}

/**
 * 转换矩形元素
 * @param {Object} drawObj 原始绘制对象
 * @param {Object} baseElement 基本元素信息
 * @returns {Object} 矩形绘制元素
 */
function convertRectangleElement(drawObj, baseElement) {
  return {
    type: 'rectangle',
    x: baseElement.x,
    y: baseElement.y,
    width: baseElement.width,
    height: baseElement.height,
    rotation: baseElement.rotation,
    lineWidth: parseFloat(drawObj.LineWidth) || 1,
    filled: drawObj.Filled === true
  };
}

/**
 * 转换图片元素
 * @param {Object} drawObj 原始绘制对象
 * @param {Object} baseElement 基本元素信息
 * @returns {Object} 图片绘制元素
 */
function convertImageElement(drawObj, baseElement) {
  return {
    type: 'image',
    x: baseElement.x,
    y: baseElement.y,
    width: baseElement.width,
    height: baseElement.height,
    rotation: baseElement.rotation,
    imagePath: drawObj.Content || drawObj.ImagePath || ''
  };
}

/**
 * 验证转换后的参数
 * @param {Object} jcParams 精臣绘制参数
 * @returns {boolean} 是否有效
 */
function validateJCParams(jcParams) {
  if (!jcParams) return false;
  
  // 检查基本参数
  if (!jcParams.canvasWidth || !jcParams.canvasHeight) {
    console.error('画布尺寸无效');
    return false;
  }
  
  if (jcParams.canvasWidth <= 0 || jcParams.canvasHeight <= 0) {
    console.error('画布尺寸必须大于0');
    return false;
  }
  
  return true;
}

module.exports = {
  mmToPx,
  pxToMm,
  convertTemplateToJCAPI,
  convertDrawObject,
  validateJCParams
};
