/**index.wxss**/
.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 240px;
}

.blockCss{
  border: 4rpx solid green;
  margin: 5rpx;
  padding: 10rpx;
  position: relative;
}

.viewRow{
  display: flex;
  flex-direction: row;

}
.viewRow-width{
  display: flex;
  flex-direction: row;
}
.viewRow-height{
  display: flex;
  flex-direction: row;
}
.viewRow-text{
  width: 220rpx;
}

.viewOneRow-text{
  width: 156rpx;
}

.inputCss{
  border: 2rpx solid gray;
  margin: 3rpx;
  border-radius: 3rpx;
  padding: 3rpx;
  font-size: 22rpx;
}

.inputOneCss{
  border: 2rpx solid gray;
  margin: 3rpx;
  border-radius: 3rpx;
  padding: 3rpx;
  font-size: 22rpx;
  
}

.onePageBlockCss{
  border: 2rpx solid rebeccapurple;
  padding: 3rpx;
}

.rowButtonCss{
  display: flex;
  flex-direction: row;
}
.elementAddButtonCss{
  font-size: 18rpx;
  padding: 3rpx;
  border: 3rpx solid red;
  margin: 5rpx;
  line-height: 80rpx;
}

.showPageCanvasCss{
  height: 80rpx;
  border: 3rpx solid red;
  margin: 5rpx;
}

.coverViewPrinters{
  position: absolute;
  top: 25%;
  left: 10%;
  width: 80%;
  height: 50%;
  background-color:#eee;
  border-radius: 3%;
}

.connectButtonCss{
  font-size: 24rpx;
  border-radius: 3%;
  background-color: white;
  width: 20%;
}


.cu-dialog{
  width: 540rpx;
  background-color:#ffffff;
  padding-bottom:70rpx;
}

.cu-dialog-close{
  width: 36rpx;
  height: 36rpx;
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjMwOTc4OTU0Njk2IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjExNzAwIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTcxNy4yIDMwNi4yYy0xNC4xLTE0LTM2LjktMTQtNTAuOSAwLjFMNTEyIDQ2MSAzNTcuNyAzMDYuM2MtMTQtMTQuMS0zNi44LTE0LjEtNTAuOS0wLjEtMTQuMSAxNC0xNC4xIDM2LjgtMC4xIDUwLjlMNDYxLjIgNTEyIDMwNi43IDY2Ni45Yy0xNCAxNC4xLTE0IDM2LjkgMC4xIDUwLjkgNyA3IDE2LjIgMTAuNSAyNS40IDEwLjVzMTguNS0zLjUgMjUuNS0xMC42TDUxMiA1NjNsMTU0LjMgMTU0LjhjNyA3LjEgMTYuMyAxMC42IDI1LjUgMTAuNnMxOC40LTMuNSAyNS40LTEwLjVjMTQuMS0xNCAxNC4xLTM2LjggMC4xLTUwLjlMNTYyLjggNTEybDE1NC40LTE1NC45YzE0LjEtMTQuMSAxNC4xLTM2LjkgMC01MC45ek01MTIgMEMyMjkuMiAwIDAgMjI5LjIgMCA1MTJzMjI5LjIgNTEyIDUxMiA1MTIgNTEyLTIyOS4yIDUxMi01MTJTNzk0LjggMCA1MTIgMHogbTMxMS4xIDgyMy4xYy00MC40IDQwLjQtODcuNSA3Mi4yLTEzOS45IDk0LjNDNjI5IDk0MC40IDU3MS40IDk1MiA1MTIgOTUyYy01OS40IDAtMTE3LTExLjYtMTcxLjItMzQuNS01Mi40LTIyLjItOTkuNC01My45LTEzOS45LTk0LjMtNDAuNC00MC40LTcyLjItODcuNS05NC4zLTEzOS45QzgzLjYgNjI5IDcyIDU3MS40IDcyIDUxMmMwLTU5LjQgMTEuNi0xMTcgMzQuNS0xNzEuMiAyMi4yLTUyLjQgNTMuOS05OS40IDk0LjMtMTM5LjkgNDAuNC00MC40IDg3LjUtNzIuMiAxMzkuOS05NC4zQzM5NSA4My42IDQ1Mi42IDcyIDUxMiA3MmM1OS40IDAgMTE3IDExLjYgMTcxLjIgMzQuNSA1Mi40IDIyLjIgOTkuNCA1My45IDEzOS45IDk0LjMgNDAuNCA0MC40IDcyLjIgODcuNSA5NC4zIDEzOS45Qzk0MC40IDM5NSA5NTIgNDUyLjYgOTUyIDUxMmMwIDU5LjQtMTEuNiAxMTctMzQuNSAxNzEuMi0yMi4yIDUyLjQtNTMuOSA5OS41LTk0LjQgMTM5Ljl6IiBwLWlkPSIxMTcwMSI+PC9wYXRoPjwvc3ZnPg==")
}

.content{
  font-size: 32rpx;
  font-weight: 600;
  line-height: 44rpx;
  color: #262626;
  padding-top: 36rpx;
}

.content-tip{
  font-size: 26rpx;
  font-weight: 400;
  line-height: 36rpx;
  color: #FB4B42;
  padding-top: 8rpx;
  
}

.connected{
  display: flex;
  padding: 0 30rpx;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 40rpx;
  color: #262626;
  margin-top: 26rpx;
}

.connected-content{
  width: 218rpx;
}

.connected-close{
  padding: 10rpx 28rpx;
  background: #F5F5F5;
  border-radius: 34rpx;
}

.connected-list{
  flex: 1;
  max-height: 50vh;
  overflow-y: scroll;
}

.connected-content-item{
  width: 100%;
  line-height: 70rpx;
}

.connected-content-list{
  width: 218rpx;
  display: inline-block;
  font-size: 24rpx;
}

.connected-close-list{
  font-size: 24rpx;
  font-weight: 400;
  line-height: 34rpx;
  color: #537FB7;
  padding: 10rpx 28rpx;
  display: inline-block;
}

.canvas{
  position:fixed;
  /* display: none; */
  left: 100%;
}

.delButtonCss{
  height: 50rpx;
  font-size: 24rpx;
}

/* 预览功能样式 */
.preview-dialog {
  width: 90vw;
  max-width: 800rpx;
  height: 80vh;
  max-height: 1200rpx;
}

.preview-content {
  padding: 30rpx;
  height: calc(100% - 120rpx);
  display: flex;
  flex-direction: column;
}

.preview-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
  min-height: 400rpx;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  border-radius: 10rpx;
}

.preview-placeholder {
  color: #999;
  font-size: 28rpx;
  text-align: center;
}

.preview-info {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-top: 10rpx;
}

.preview-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  flex-wrap: wrap;
  gap: 20rpx;
}

.preview-nav {
  display: flex;
  gap: 20rpx;
}

.preview-btn-small {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  min-width: 120rpx;
}

.preview-btn-small:active {
  background-color: #0056cc;
}

.preview-btn {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
  padding: 15rpx 30rpx;
}

.preview-btn:active {
  background-color: #1e7e34;
}

.save-btn {
  background-color: #ff9500 !important;
}

.save-btn:active {
  background-color: #cc7700 !important;
}