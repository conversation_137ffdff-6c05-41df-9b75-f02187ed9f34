// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
  },
  globalData: {
    userInfo: null
  },

  onShow(options){
    wx.getClipboardData({
      success: (option) => {
        let str = option.data;
        let head = '$永无bug¥';
        let tail = '¥永无bug$';
        let idx1 = str.indexOf(head);
        let idx2 = str.indexOf(tail);
        if(idx1 != -1 && idx2 != -1){
          str = str.substring(head.length);
          str = str.substring(0,str.length-tail.length);
          let obj = JSON.parse(str);
          if(typeof(obj) == typeof({})){
            wx.showModal({
              cancelColor: '#e9e9e9',
              title:'检测到模版数据',
              content:'存储为:'+obj.name,
              showCancel:true,
              editable:false,
              placeholderText:'输入保存的名称',
              confirmColor:'#00ff00',
              success:(res)=>{
                if(res.confirm){
                  let item = []
                  if (wx.getStorageSync('saveDatas')){
                    item = wx.getStorageSync('saveDatas')
                  }
                  item.unshift(obj);
                  wx.setStorageSync('saveDatas', item)
                }else if (res.cancel) {
                  console.log('用户点击取消')
                }
              }
            })
          }
        }
      },
    })
  }
})
