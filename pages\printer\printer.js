// printer.js - 精臣B1打印机对接版本
// 使用精臣JCAPI SDK替代原有的SUPVANAPIT50PRO SDK
import JCAPI from '../../utils/JCAPI/JCAPI.js'
import JCAPIErrorCode from '../../utils/JCAPI/JCAPIErrorCode.js'

// 导入配置文件
const onlineStoreConfig = require('../../config/onlineStore.js');
const {
  getStatusMessage,
  getErrorLevel,
  getStatusCategory,
  isSuccessStatus
} = require('../../config/printerStatus.js');
const { getDefaultTemplates } = require('../../config/templates.js');
const { isAllowedPrinter, getPrinterInfo } = require('../../config/printerSn.js');
const { convertTemplateToJCAPI, validateJCParams } = require('../../utils/templateConverter.js');

Page({
  data: {
    // 打印机状态
    printerStatus: 'disconnected', // disconnected, connecting, error, connected, printing
    printerDeviceSn: '',
    printerErrorCode: null, // 错误状态码
    printerErrorMessage: '', // 错误信息
    isPrinting: false, // 是否正在打印
    isConnecting: false, // 是否正在连接

    // 模版相关
    templates: [], // 模版列表
    selectedTemplateIndex: 0, // 当前选中的模版索引
    previewImagePath: '', // 预览图路径
    previewGenerating: false, // 是否正在生成预览图
    currentPreviewId: 0, // 当前预览图生成ID，用于防止竞态条件

    // Canvas 尺寸
    canvasWidth: 320, // 默认canvas宽度
    canvasHeight: 240, // 默认canvas高度

    // 标签内容
    labelContent: {
      productName: '品名',
      operator: '操作人',
      date: '',
      copies: 1
    },

    // 蓝牙相关
    blueList: [],
    isScanning: false,
    showDeviceSelector: false,

    // 联系我们弹窗
    showContactModal: false,

    // 系统信息
    statusBarHeight: 0, // 状态栏高度
    navBarHeight: 40, // 导航栏高度，默认40px

    // Canvas相关
    templateWidth: 560,
    templateHeight: 302,
    barCodeWidth: 214,
    barCodeHeight: 72,
    qrCodeWidth: 20,
    qrCodeHeight: 20,
    pixelRatio: 2,
    canvasBarCode: null,
    canvasText: null,

    // 耗材信息相关
    materialInfo: null, // 当前耗材信息
    materialMismatch: false, // 耗材规格是否不匹配
    forcePrint: false // 是否允许强制打印
  },



  onLoad() {
    this.getSystemInfo()
    this.initPage()
  },


  onReady() {
    // 尝试连接上次使用的打印机
    // this.tryConnectLastPrinter()
  },

  onUnload(){
    this.disconnectDevice()
  },

  /**
   * 获取系统信息，设置状态栏和导航栏高度
   */
  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync()
    const { statusBarHeight, platform } = systemInfo

    // 设置状态栏高度
    this.setData({
      statusBarHeight: statusBarHeight || 20
    })

    // 根据平台设置导航栏高度
    let navBarHeight = 40 // iOS默认高度，减小一些
    if (platform === 'android') {
      navBarHeight = 46 // Android稍高一些
    }

    this.setData({
      navBarHeight: navBarHeight
    })

    console.log('系统信息:', { statusBarHeight, navBarHeight, platform })
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 设置当前日期
    const today = new Date()
    const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`

    // 加载上次保存的标签内容
    const savedContent = wx.getStorageSync('lastLabelContent')
    const labelContent = savedContent ? {
      ...savedContent,
      date: /*savedContent.date ||*/ dateStr // 如果没有保存日期，使用当前日期
    } : {
      productName: '品名',
      operator: '操作人',
      date: dateStr,
      copies: 1
    }

    this.setData({
      labelContent: labelContent,
      pixelRatio: wx.getWindowInfo().pixelRatio,
      canvasText: wx.createCanvasContext('Canvas', this),
      canvasBarCode: wx.createSelectorQuery().in(this)
    })

    // 加载模版配置
    this.loadTemplates()

    // 尝试连接上次使用的打印机
    // this.tryConnectLastPrinter()
  },

  /**
   * 加载模版配置
   */
  loadTemplates() {
    // 从本地存储获取上次选中的模版索引
    const lastSelectedIndex = wx.getStorageSync('lastSelectedTemplateIndex') || 0

    // 尝试从网络加载模版配置文件，如果失败则使用默认配置
    this.loadTemplatesFromNetwork().then(templates => {
      this.setData({
        templates: templates,
        selectedTemplateIndex: lastSelectedIndex < templates.length ? lastSelectedIndex : 0
      })
      this.generatePreview()
    }).catch(() => {
      // 网络加载失败，使用默认配置
      const templates = getDefaultTemplates()

    
      this.setData({
        templates: templates,
        selectedTemplateIndex: lastSelectedIndex < templates.length ? lastSelectedIndex : 0
      })
      this.generatePreview()
    })
  },

  /**
   * 从网络加载模版配置
   */
  loadTemplatesFromNetwork() {
    return new Promise((resolve, reject) => {
      // 这里可以从服务器加载配置文件
      // 目前使用本地默认配置
      reject('使用本地配置')
    })
  },

  /**
   * 尝试连接上次使用的打印机
   */
  tryConnectLastPrinter() {
    const lastPrinter = wx.getStorageSync('lastConnectedPrinter')
    if (lastPrinter) {
      this.setData({
        printerDeviceSn: lastPrinter.name || lastPrinter.deviceId
      })
      // 这里可以尝试连接上次的打印机
      // bleTool.connectBleDevice(lastPrinter)...
      this.connectDevice(lastPrinter)
    }
  },

  /**
   * 选择模版
   */
  selectTemplate(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      selectedTemplateIndex: index
    })

    // 保存选择到本地存储
    wx.setStorageSync('lastSelectedTemplateIndex', index)

    // 检查耗材规格匹配
    if (this.data.printerStatus === 'connected') {
      this.checkMaterialCompatibility()
    }

    // 重新生成预览图
    this.generatePreview()
  },

  /**
   * 生成预览图 - 使用精臣JCAPI和模板转换工具
   */
  generatePreview() {
    const template = this.data.templates[this.data.selectedTemplateIndex]
    if (!template) return

    // 如果正在生成预览图，跳过本次请求
    if (this.data.previewGenerating) {
      console.log('预览图正在生成中，跳过本次请求')
      return
    }

    // 生成唯一的预览ID，用于防止竞态条件
    const previewId = ++this.data.currentPreviewId

    // 设置生成状态
    this.setData({
      previewGenerating: true
    })

    const that = this

    try {
      // 使用模板转换工具转换配置
      const jcParams = convertTemplateToJCAPI(template, this.data.labelContent)

      // 验证转换结果
      if (!validateJCParams(jcParams)) {
        throw new Error('模板转换失败')
      }

      console.log('转换后的绘制参数:', jcParams)
      console.log('模板尺寸:', jcParams.templateWidth, 'x', jcParams.templateHeight, 'mm')
      console.log('画布尺寸:', jcParams.canvasWidth, 'x', jcParams.canvasHeight, 'px')
      console.log('绘制元素数量:', jcParams.drawElements.length)

      // 设置canvas尺寸
      this.setData({
        canvasWidth: jcParams.canvasWidth,
        canvasHeight: jcParams.canvasHeight
      })

      // 开始绘制标签 - JCAPI.startDrawLabel 期望毫米尺寸
      JCAPI.startDrawLabel('previewCanvas', this, jcParams.templateWidth, jcParams.templateHeight, jcParams.rotation)

      // 绘制所有元素
      jcParams.drawElements.forEach((element, index) => {
        console.log(`绘制元素 ${index}:`, element)
        this.drawElement(element)
      })

      // 结束绘制并生成预览图
      JCAPI.endDrawLabel(function() {
        // 检查是否为最新的预览请求
        if (previewId !== that.data.currentPreviewId) {
          console.log(`预览图请求已过期，忽略结果。当前ID: ${that.data.currentPreviewId}, 请求ID: ${previewId}`)
          return
        }

        // 生成预览图片
        wx.canvasToTempFilePath({
          x: 0,
          y: 0,
          width: jcParams.canvasWidth,
          height: jcParams.canvasHeight,
          canvasId: 'previewCanvas',
          success: (res) => {
            that.setData({
              previewImagePath: res.tempFilePath,
              previewGenerating: false
            })
            console.log('预览图生成成功，显示尺寸:', that.data.previewDisplayWidth, 'x', that.data.previewDisplayHeight, 'px')
          },
          fail: (error) => {
            console.error('生成预览图片失败:', error)
            that.setData({
              previewGenerating: false
            })
          }
        }, that)
      })

    } catch (error) {
      console.error('生成预览图失败:', error)
      // 重置生成状态
      that.setData({
        previewGenerating: false
      })
    }
  },

  /**
   * 绘制单个元素 - 使用精臣JCAPI
   * @param {Object} element 绘制元素
   */
  drawElement(element) {
    if (!element) return

    // 将像素坐标转换为毫米坐标（B1打印机200dpi，1mm=8px）
    const pxToMm = (px) => Math.round(px / 8 * 100) / 100

    switch (element.type) {
      case 'text':
        // JCAPI.drawText(content, x, y, fontHeight, rotation, options)
        // 坐标需要转换为毫米
        JCAPI.drawText(
          element.content,
          pxToMm(element.x),
          pxToMm(element.y),
          element.fontSize, // 字体大小已经是毫米
          element.rotation,
          element.options
        )
        break

      case 'barcode':
        // JCAPI.drawBarcode(content, x, y, width, height, rotation, fontSize, fontHeight, position)
        JCAPI.drawBarcode(
          element.content,
          pxToMm(element.x),
          pxToMm(element.y),
          pxToMm(element.width),
          pxToMm(element.height),
          element.rotation,
          element.fontSize,
          element.fontHeight,
          element.fontPosition
        )
        break

      case 'qrcode':
        // JCAPI.drawQRCode(content, x, y, width, height, rotation)
        JCAPI.drawQRCode(
          element.content,
          pxToMm(element.x),
          pxToMm(element.y),
          pxToMm(element.width),
          pxToMm(element.height),
          element.rotation
        )
        break

      case 'line':
        // JCAPI.drawLine(x, y, width, height, rotation)
        JCAPI.drawLine(
          pxToMm(element.x),
          pxToMm(element.y),
          pxToMm(element.width),
          pxToMm(element.height),
          element.rotation
        )
        break

      case 'rectangle':
        // JCAPI.drawRectangle(x, y, width, height, lineWidth, isFilled, rotation)
        JCAPI.drawRectangle(
          pxToMm(element.x),
          pxToMm(element.y),
          pxToMm(element.width),
          pxToMm(element.height),
          element.lineWidth,
          element.filled,
          element.rotation
        )
        break

      case 'image':
        if (element.imagePath) {
          // JCAPI.drawImage(path, x, y, width, height, rotation)
          JCAPI.drawImage(
            element.imagePath,
            pxToMm(element.x),
            pxToMm(element.y),
            pxToMm(element.width),
            pxToMm(element.height),
            element.rotation
          )
        }
        break

      default:
        console.warn('未知的绘制元素类型:', element.type)
    }
  },

  /**
   * 保存标签内容到本地存储
   */
  saveLabelContent() {
    wx.setStorageSync('lastLabelContent', this.data.labelContent)
  },

  /**
   * 获取耗材信息 - B1设备适配版本
   * 由于精臣B1不支持getPageInfos获取耗材信息，这里模拟兼容的耗材信息
   */
  getMaterialInfo() {
    const that = this
    return new Promise((resolve, reject) => {
      console.log('B1设备不支持获取耗材信息，使用模拟数据...')

      // 模拟耗材信息，设为与当前模板兼容
      const currentTemplate = this.data.templates[this.data.selectedTemplateIndex]
      if (currentTemplate) {
        const materialInfo = {
          paperDirectionSize: currentTemplate.Height, // 纸张高度
          printHeadDirectionSize: currentTemplate.Width, // 纸张宽度
          gap: currentTemplate.Gap, // B1不支持gap设置，设为与当前模板兼容
          deviceModel: 'B1', // 设备型号
          compatible: true // 标记为兼容
        }

        console.log('模拟耗材信息:', materialInfo)
        that.setData({
          materialInfo: materialInfo
        })
        resolve(materialInfo)
      } else {
        // 如果没有模板，使用默认值
        const defaultMaterialInfo = {
          paperDirectionSize: 30,
          printHeadDirectionSize: 40,
          gap: 0,
          deviceModel: 'B1',
          compatible: true
        }

        console.log('使用默认耗材信息:', defaultMaterialInfo)
        that.setData({
          materialInfo: defaultMaterialInfo
        })
        resolve(defaultMaterialInfo)
      }
    })
  },

  /**
   * 检查耗材规格兼容性
   * @param {boolean} forceRefresh - 是否强制重新获取耗材信息，默认false使用缓存
   */
  checkMaterialCompatibility(forceRefresh = false) {
    if (!this.data.materialInfo || forceRefresh) {
      // 如果没有耗材信息或强制刷新，先获取最新信息
      console.log('重新获取耗材信息进行兼容性检查...')
      this.getMaterialInfo().then(() => {
        this.performCompatibilityCheck()
      }).catch(error => {
        console.error('无法获取耗材信息进行兼容性检查:', error)
      })
    } else {
      console.log('使用缓存的耗材信息进行兼容性检查')
      this.performCompatibilityCheck()
    }
  },

  /**
   * 执行兼容性检查
   */
  performCompatibilityCheck() {
    const template = this.data.templates[this.data.selectedTemplateIndex]
    const materialInfo = this.data.materialInfo

    console.log('执行兼容性检查:', {
      hasTemplate: !!template,
      hasMaterialInfo: !!materialInfo,
      template: template ? {
        name: template.TemplateName,
        width: template.Width,
        height: template.Height,
        gap: template.Gap
      } : null,
      materialInfo: materialInfo
    })

    if (!template || !materialInfo) {
      console.log('兼容性检查跳过：缺少模板或耗材信息')
      return
    }

    // 比较耗材规格与模版规格
    // paperDirectionSize 对应 Height
    // printHeadDirectionSize 对应 Width
    // gap 对应 Gap
    const heightMatch = materialInfo.paperDirectionSize === template.Height
    const widthMatch = materialInfo.printHeadDirectionSize === template.Width
    const gapMatch = materialInfo.gap === template.Gap

    const isCompatible = heightMatch && widthMatch && gapMatch

    console.log('兼容性检查结果:', {
      template: {
        width: template.Width,
        height: template.Height,
        gap: template.Gap
      },
      material: {
        width: materialInfo.printHeadDirectionSize,
        height: materialInfo.paperDirectionSize,
        gap: materialInfo.gap
      },
      match: {
        heightMatch,
        widthMatch,
        gapMatch
      },
      isCompatible: isCompatible
    })

    this.setData({
      materialMismatch: !isCompatible
    })

    if (!isCompatible) {
      // 更新状态栏显示不匹配信息
      this.setData({
        printerErrorMessage: '当前耗材规格与模版不一致',
        printerErrorCode: null
      })

      console.log('⚠️ 耗材规格不匹配 - 已设置 materialMismatch = true')
    } else {
      // 规格匹配，清除错误信息
      if (this.data.printerErrorMessage === '当前耗材规格与模版不一致') {
        this.setData({
          printerErrorMessage: '',
          printerErrorCode: null
        })
      }
      console.log('✅ 耗材规格匹配 - 已设置 materialMismatch = false')
    }
  },



  /**
   * 处理打印机状态码
   */
  handlePrinterStatus(resultCode, useUserFriendlyMessage = false) {
    const message = getStatusMessage(resultCode, useUserFriendlyMessage);
    const category = getStatusCategory(resultCode);
    const errorLevel = getErrorLevel(resultCode);
    const isSuccess = isSuccessStatus(resultCode);

    // 成功状态码 - 设备连接正常
    if (isSuccess) {
      return {
        status: 'connected',
        message: message,
        isConnected: true,
        category: category,
        errorLevel: errorLevel
      }
    }

    // 硬件/耗材相关错误 - 设备已连接但打印有问题
    if (category === 'hardware') {
      return {
        status: 'connected', // 设备仍然连接，只是打印有问题
        message: message,
        isPrintError: true,
        isConnected: true,
        category: category,
        errorLevel: errorLevel
      }
    }

    // 蓝牙连接相关错误 - 设备连接问题
    if (category === 'bluetooth') {
      return {
        status: 'error',
        message: message,
        isConnected: false,
        category: category,
        errorLevel: errorLevel
      }
    }

    // 数据处理错误 - 根据具体情况判断
    if (category === 'dataProcessing') {
      // 某些数据处理错误不影响连接状态
      const nonConnectionErrors = [108, 116, 118, 119, 120, 121, 122, 123, 124, 135];
      const isConnected = nonConnectionErrors.includes(resultCode);

      return {
        status: isConnected ? 'connected' : 'error',
        message: message,
        isConnected: isConnected,
        isPrintError: isConnected,
        category: category,
        errorLevel: errorLevel
      }
    }

    // 其他未知错误
    return {
      status: 'error',
      message: message,
      isConnected: false,
      category: 'unknown',
      errorLevel: 'error'
    }
  },

  /**
   * 更新打印机状态
   */
  updatePrinterStatus(resultCode, customMessage = '', useUserFriendlyMessage = false) {
    const statusInfo = this.handlePrinterStatus(resultCode, useUserFriendlyMessage)

    this.setData({
      printerStatus: statusInfo.status,
      printerErrorCode: resultCode,
      printerErrorMessage: customMessage || statusInfo.message
    })

    // 如果设备仍然连接，保持设备信息
    if (statusInfo.isConnected && this.data.printerDeviceSn) {
      // 保持设备连接信息不变
    } else if (!statusInfo.isConnected) {
      // 设备断开连接，清除设备信息
      this.setData({
        printerDeviceSn: ''
      })
    }
  },

  /**
   * 显示用户友好的错误提示
   */
  showUserFriendlyError(resultCode, title = '操作失败') {
    const userFriendlyMessage = getStatusMessage(resultCode, true);
    const errorLevel = getErrorLevel(resultCode);

    // 根据错误级别选择不同的显示方式
    if (errorLevel === 'warning') {
      wx.showModal({
        title: '提示',
        content: userFriendlyMessage,
        showCancel: false,
        confirmText: '知道了'
      });
    } else if (errorLevel === 'error') {
      wx.showModal({
        title: title,
        content: userFriendlyMessage,
        showCancel: false,
        confirmText: '确定'
      });
    } else {
      wx.showToast({
        title: userFriendlyMessage,
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 输入品名
   */
  onProductNameInput(e) {
    this.setData({
      'labelContent.productName': e.detail.value.slice(0, 20) // 限制20个字符
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 输入操作人
   */
  onOperatorInput(e) {
    this.setData({
      'labelContent.operator': e.detail.value.slice(0, 20) // 限制20个字符
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 选择日期
   */
  onDateChange(e) {
    this.setData({
      'labelContent.date': e.detail.value
    })
    this.saveLabelContent() // 保存内容
    this.generatePreview()
  },

  /**
   * 输入打印份数
   */
  onCopiesInput(e) {
    let value = e.detail.value
    // 允许空值，用户可能正在删除内容
    if (value === '') {
      this.setData({
        'labelContent.copies': ''
      })
      return
    }

    let copies = parseInt(value) || 1
    copies = Math.max(1, Math.min(250, copies)) // 限制1-250
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 增加打印份数
   */
  increaseCopies() {
    let copies = parseInt(this.data.labelContent.copies) || 1
    copies = Math.min(250, copies + 1) // 最大250
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 减少打印份数
   */
  decreaseCopies() {
    let copies = parseInt(this.data.labelContent.copies) || 1
    copies = Math.max(1, copies - 1) // 最小1
    this.setData({
      'labelContent.copies': copies
    })
    this.saveLabelContent() // 保存内容
    // 移除预览图刷新，因为份数与内容无关
  },

  /**
   * 连接打印机/打印标签/停止打印按钮
   */
  onPrintAction() {
    // 防止重复操作
    if (this.data.isConnecting || this.data.isScanning) {
      const message = this.data.isScanning ? '正在搜索设备...' : '正在连接中...'
      wx.showToast({
        title: message,
        icon: 'none'
      })
      return
    }

    if (this.data.isPrinting) {
      // 正在打印，停止打印
      this.stopPrint()
    } else if (this.data.printerStatus === 'connected') {
      // 已连接，执行打印
      this.printLabel()
    } else {
      // 未连接，开始连接流程
      this.startConnectPrinter()
    }
  },

  /**
   * 开始连接打印机流程
   */
  startConnectPrinter() {
    // 检查蓝牙授权
    wx.authorize({
      scope: 'scope.bluetooth',
      success: () => {
        this.scanAndConnectPrinter()
      },
      fail: () => {
        wx.showModal({
          title: '需要蓝牙权限',
          content: '请授权蓝牙功能以连接打印机',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting()
            }
          }
        })
      }
    })
  },

  /**
   * 扫描并连接打印机 - 使用精臣JCAPI
   */
  scanAndConnectPrinter() {
    this.setData({
      isScanning: true,
      blueList: []
    })

    const that = this
    let foundDevices = []
    let scanComplete = false

    // 5秒后停止扫描并处理结果
    const scanTimeout = setTimeout(() => {
      scanComplete = true
      that.setData({ isScanning: false })

      console.log('扫描完成，找到设备:', foundDevices)
      console.log('当前尝试连接设备:', foundDevices[0])

      if (foundDevices.length === 1) {
        // 只有一台设备，直接连接
        that.connectDevice(foundDevices[0])
      } else if (foundDevices.length > 1) {
        // 多台设备，显示选择对话框
        that.setData({
          blueList: foundDevices,
          showDeviceSelector: true
        })
      } else {
        // 没有找到设备
        that.setData({
          printerStatus: 'error',
          printerErrorMessage: '未找到可用的打印机',
          printerErrorCode: null
        })
        wx.showToast({
          title: '未找到可用的打印机',
          icon: 'none'
        })
      }
    }, 5000)

    // 使用精臣SDK扫描设备
    JCAPI.scanedPrinters((didGetScanedPrinters) => {
      if (scanComplete) return // 如果扫描已完成，忽略后续回调

      console.log('精臣SDK扫描到设备:', didGetScanedPrinters)

      // 过滤掉未知设备和不在允许列表中的设备
      let filteredDevices = didGetScanedPrinters.filter(device => {
        // 过滤未知设备
        if (device.name && device.name.indexOf('未知') >= 0) {
          return false
        }

        // 检查设备是否在允许列表中（通过设备名称检查序列号）
        if (device.name && isAllowedPrinter(device.name)) {
          return true
        }

        console.log('设备不在允许列表中:', device.name)
        return false
      })

      console.log('过滤后的设备列表:', filteredDevices)

      // 累积找到的设备，避免重复
      filteredDevices.forEach(device => {
        const exists = foundDevices.find(item =>
          item.name === device.name || item.deviceId === device.deviceId
        )
        if (!exists) {
          foundDevices.push(device)
        }
      })

      // 实时更新设备列表显示
      that.setData({
        blueList: foundDevices
      })

      // 如果找到了授权设备，可以提前结束扫描
      if (foundDevices.length > 0 && !scanComplete) {
        console.log('找到授权设备，继续扫描中...')
      }

      // 如果没有找到允许的设备但扫描到了其他设备，提示用户
      if (foundDevices.length === 0 && didGetScanedPrinters.length > 0) {
        console.log('扫描到设备但不在授权列表中')
      }
    })
  },

  /**
   * 连接指定设备 - 使用精臣JCAPI
   */
  connectDevice(device) {
    // 设置连接中状态
    this.setData({
      isConnecting: true,
      printerStatus: 'connecting',
      printerErrorCode: null,
      printerErrorMessage: ''
    })

    const that = this

    // 使用精臣SDK连接打印机
    JCAPI.openPrinter(device.name,
      // 连接成功回调
      function() {
        console.log('精臣SDK连接成功:', device.name)

        // 设备连接成功
        that.setData({
          isConnecting: false,
          printerStatus: 'connected',
          printerDeviceSn: device.name || device.deviceId,
          showDeviceSelector: false,
          printerErrorCode: null,
          printerErrorMessage: ''
        })

        // 保存到本地存储
        wx.setStorageSync('lastConnectedPrinter', device)

        // 更新模版的DeviceSn
        const templates = that.data.templates
        templates.forEach(template => {
          template.DeviceSn = device.name || device.deviceId
        })
        that.setData({ templates })

        // B1设备不支持getPageInfos获取耗材信息，默认设为兼容
        that.setData({
          materialInfo: {
            // 模拟耗材信息，设为与模板兼容
            paperDirectionSize: that.data.templates[that.data.selectedTemplateIndex]?.Height || 30,
            printHeadDirectionSize: that.data.templates[that.data.selectedTemplateIndex]?.Width || 40,
            gap: 0 // B1不支持gap设置
          },
          materialMismatch: false
        })

        wx.showToast({
          title: '连接成功',
          icon: 'success'
        })
      },
      // 连接断开回调
      function() {
        console.log('精臣SDK连接断开:', device.name)

        that.setData({
          isConnecting: false,
          printerStatus: 'disconnected',
          printerDeviceSn: '',
          printerErrorCode: null,
          printerErrorMessage: '设备连接断开'
        })

        wx.showToast({
          title: '设备连接断开',
          icon: 'none'
        })
      }
    )
  },

  /**
   * 选择设备对话框中的设备选择
   */
  selectDevice(e) {
    const index = e.currentTarget.dataset.index
    const device = this.data.blueList[index]

    // 停止扫描 - 使用精臣JCAPI停止扫描
    if (this.data.isScanning) {
      // 精臣SDK会自动管理扫描状态，这里只需要更新UI状态
      this.setData({
        isScanning: false
      })
    }

    // 检查是否为当前已连接设备
    const currentDeviceId = this.data.printerDeviceSn
    const selectedDeviceId = device.name || device.deviceId

    if (currentDeviceId === selectedDeviceId) {
      // 选择的是当前设备，不需要重新连接
      wx.showToast({
        title: '当前设备已连接',
        icon: 'success'
      })
      this.setData({
        showDeviceSelector: false
      })
      return
    }

    // 连接新设备
    this.connectDevice(device)
  },

  /**
   * 关闭设备选择对话框
   */
  closeDeviceSelector() {
    // 清除扫描定时器
    if (this.scanTimer) {
      clearTimeout(this.scanTimer)
      this.scanTimer = null
    }

    // 停止扫描并关闭对话框
    this.setData({
      showDeviceSelector: false,
      isScanning: false,
      blueList: [] // 清空设备列表
    })

    console.log('设备选择对话框已关闭')
  },

  /**
   * 更改设备 - 使用精臣JCAPI
   */
  changeDevice() {
    // 防止重复操作
    if (this.data.isScanning || this.data.isConnecting) {
      wx.showToast({
        title: '操作进行中，请稍候',
        icon: 'none'
      })
      return
    }

    // 开始搜索设备并显示选择对话框
    this.setData({
      isScanning: true,
      blueList: [],
      showDeviceSelector: true
    })

    const that = this
    let scanTimer = null

    // 设置扫描超时，10秒后自动停止扫描
    scanTimer = setTimeout(() => {
      if (that.data.isScanning) {
        that.setData({
          isScanning: false
        })
        console.log('扫描超时，自动停止')
      }
    }, 10000)

    // 使用精臣SDK扫描设备
    JCAPI.scanedPrinters((didGetScanedPrinters) => {
      // 如果对话框已关闭，停止处理扫描结果
      if (!that.data.showDeviceSelector) {
        console.log('设备选择对话框已关闭，停止处理扫描结果')
        return
      }

      console.log('更改设备 - 扫描到设备:', didGetScanedPrinters)

      // 过滤设备
      let filteredDevices = didGetScanedPrinters.filter(device => {
        // 过滤未知设备
        if (device.name && device.name.indexOf('未知') >= 0) {
          return false
        }

        // 检查设备是否在允许列表中
        if (device.name && isAllowedPrinter(device.name)) {
          // 检查是否已存在
          const exists = that.data.blueList.find(item =>
            (item.deviceId === device.deviceId) || (item.name === device.name)
          )
          return !exists
        }

        return false
      })

      // 添加新发现的设备
      if (filteredDevices.length > 0) {
        const newBlueList = [...that.data.blueList, ...filteredDevices]
        that.setData({
          blueList: newBlueList
        })
      }
    })

    // 保存定时器引用，以便在关闭对话框时清除
    this.scanTimer = scanTimer
  },

  /**
   * 打印标签
   */
  printLabel() {
    const template = this.data.templates[this.data.selectedTemplateIndex]
    if (!template) {
      this.updatePrinterStatus(116) // 模板对象不能为空
      wx.showToast({
        title: '请选择模版',
        icon: 'none'
      })
      return
    }



    // 每次打印前都重新获取最新的耗材信息，避免缓存问题
    console.log('打印前重新获取最新耗材信息...')
    this.getMaterialInfo().then(() => {
      console.log('最新耗材信息获取完成，开始兼容性检查')
      this.performCompatibilityCheck()

      console.log('当前耗材匹配状态:', {
        materialMismatch: this.data.materialMismatch,
        forcePrint: this.data.forcePrint,
        materialInfo: this.data.materialInfo,
        template: {
          name: template.TemplateName,
          width: template.Width,
          height: template.Height,
          gap: template.Gap
        }
      })

      // 检查耗材规格兼容性
      if (this.data.materialMismatch) {
        // 如果设置了强制打印，弹出确认对话框
        if (this.data.forcePrint) {
          wx.showModal({
            title: '耗材规格不匹配',
            content: '当前耗材规格与模版不一致，确定要强制打印吗？',
            confirmText: '确定打印',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                // 用户确认强制打印
                this.executePrint()
              }
            }
          })
          return
        } else {
          // 如果没有设置强制打印，直接阻止打印
          wx.showToast({
            title: '耗材规格不匹配，无法打印',
            icon: 'none',
            duration: 2000
          })
          return
        }
      }

      this.executePrint()
    }).catch(error => {
      console.error('获取耗材信息失败，无法进行兼容性检查:', error)
      wx.showToast({
        title: '获取耗材信息失败，请检查设备连接',
        icon: 'none',
        duration: 2000
      })
    })
  },

  /**
   * 执行打印 - 使用精臣JCAPI和模板转换工具
   */
  executePrint() {
    // 设置打印状态
    this.setData({
      isPrinting: true,
      printerStatus: 'printing'
    })

    const template = this.data.templates[this.data.selectedTemplateIndex]
    const copies = parseInt(this.data.labelContent.copies) || 1
    const that = this

    try {
      // 使用模板转换工具转换配置
      const jcParams = convertTemplateToJCAPI(template, this.data.labelContent)

      // 验证转换结果
      if (!validateJCParams(jcParams)) {
        throw new Error('模板转换失败')
      }

      console.log('打印 - 转换后的绘制参数:', jcParams)

      // 设置打印参数
      const gapType = 1 // B1默认间隙类型
      const darkness = jcParams.density // 打印浓度

      // 设置打印完成的超时检查，防止状态卡死
      let printTimeoutTimer = setTimeout(() => {
        if (that.data.isPrinting) {
          console.log('打印超时，重置状态')
          that.setData({
            isPrinting: false,
            printerStatus: 'connected',
            printerErrorCode: null,
            printerErrorMessage: ''
          })
        }
      }, 30000) // 30秒超时

      // 监听打印进度
      JCAPI.didReadPrintCountInfo(function(res) {
        console.log('打印进度:', res)
        // 可以在这里更新打印进度UI
      })

      // 监听打印错误
      JCAPI.didReadPrintErrorInfo(function(res) {
        console.log('打印错误:', res)

        // 清除超时定时器
        if (printTimeoutTimer) {
          clearTimeout(printTimeoutTimer)
          printTimeoutTimer = null
        }

        that.setData({
          isPrinting: false,
          printerStatus: 'error',
          printerErrorCode: res.errCode,
          printerErrorMessage: res.msg || '打印出错'
        })

        wx.showToast({
          title: `打印失败: ${res.msg}`,
          icon: 'none',
          duration: 3000
        })
      })

      // 开始打印任务
      JCAPI.startJob(gapType, darkness, copies, function() {
        console.log('精臣SDK开始打印任务')

        // 开始绘制标签 - JCAPI.startDrawLabel 期望毫米尺寸
        JCAPI.startDrawLabel('printCanvas', that, jcParams.templateWidth, jcParams.templateHeight, jcParams.rotation)

        // 绘制所有元素
        jcParams.drawElements.forEach(element => {
          that.drawElement(element)
        })

        // 结束绘制并执行打印
        JCAPI.endDrawLabel(function() {
          JCAPI.print(copies, function() {
            console.log('精臣SDK打印完成')

            // 清除超时定时器
            if (printTimeoutTimer) {
              clearTimeout(printTimeoutTimer)
              printTimeoutTimer = null
            }

            // 打印成功，立即更新状态
            that.setData({
              isPrinting: false,
              printerStatus: 'connected',
              printerErrorCode: null,
              printerErrorMessage: ''
            })

            wx.showToast({
              title: '打印成功',
              icon: 'success'
            })
          })
        })
      })

    } catch (error) {
      console.log('打印异常:', error)
      that.setData({
        isPrinting: false,
        printerStatus: 'error',
        printerErrorCode: 132,
        printerErrorMessage: '打印异常终止'
      })
      wx.showToast({
        title: '打印异常终止',
        icon: 'error'
      })
    }
  },

  /**
   * 停止打印 - 使用精臣JCAPI
   */
  stopPrint() {
    if (!this.data.isPrinting) {
      wx.showToast({
        title: '当前没有打印任务',
        icon: 'none'
      })
      return
    }

    // 立即更新UI状态，避免用户重复点击
    this.setData({
      isPrinting: false,
      printerStatus: 'connected',
      printerErrorCode: null,
      printerErrorMessage: ''
    })

    // 使用精臣SDK取消打印
    JCAPI.cancelPrint(() => {
      console.log('精臣SDK取消打印成功')
      wx.showToast({
        title: '已停止打印',
        icon: 'success'
      })
    })
  },

  /**
   * 断开蓝牙 - 使用精臣JCAPI
   */
  disconnectDevice() {
    console.log('断开精臣B1打印机连接')

    // 使用精臣SDK关闭打印机连接
    JCAPI.closePrinter()

    // 更新状态
    this.setData({
      printerStatus: 'disconnected',
      printerDeviceSn: '',
      printerErrorCode: null,
      printerErrorMessage: '',
      materialInfo: null,
      materialMismatch: false
    })

    // 清除本地存储
    wx.removeStorageSync('lastConnectedPrinter')

    wx.showToast({
      title: '已断开连接',
      icon: 'success'
    })
  },
  /**
   * 打开在线商城
   */
  openOnlineStore() {
    // 检查配置是否完整
    if (onlineStoreConfig.appId === 'YOUR_TARGET_APPID' || onlineStoreConfig.appId === '') {
      wx.showModal({
        title: '配置提醒',
        content: onlineStoreConfig.errorMessages.configNotSet,
        showCancel: false
      });
      return;
    }

    // 构建传递给目标小程序的参数
    const extraData = {
      // 来源标识
      source: 'printer_miniprogram',

      // 时间戳
      timestamp: Date.now()
    };

    // 根据配置决定是否传递设备信息
    if (onlineStoreConfig.passDeviceInfo && this.data.printerDeviceSn) {
      extraData.deviceSn = this.data.printerDeviceSn;
      extraData.printerStatus = this.data.printerStatus;
    }

    // 根据配置决定是否传递标签内容
    if (onlineStoreConfig.passLabelContent) {
      extraData.labelContent = this.data.labelContent;
    }

    // 根据配置决定是否传递模板信息
    if (onlineStoreConfig.passTemplateInfo && this.data.templates[this.data.selectedTemplateIndex]) {
      extraData.selectedTemplate = this.data.templates[this.data.selectedTemplateIndex];
    }

    // 添加自定义业务参数
    if (onlineStoreConfig.customParams && Object.keys(onlineStoreConfig.customParams).length > 0) {
      Object.assign(extraData, onlineStoreConfig.customParams);
    }

    // 调试模式下打印参数
    if (onlineStoreConfig.debug) {
      console.log('跳转在线商城参数:', {
        appId: onlineStoreConfig.appId,
        path: onlineStoreConfig.path,
        extraData: extraData
      });
    }

    wx.navigateToMiniProgram({
      appId: onlineStoreConfig.appId,
      path: onlineStoreConfig.path,
      extraData: extraData,
      envVersion: onlineStoreConfig.envVersion,
      success: (res) => {
        if (onlineStoreConfig.debug) {
          console.log('跳转在线商城成功', res);
        }
      },
      fail: (err) => {
        console.error('跳转在线商城失败', err);

        // 根据错误类型显示相应的提示信息
        let errorMessage = onlineStoreConfig.errorMessages.defaultError;

        if (err.errMsg && err.errMsg.includes('navigateToMiniProgram:fail can not navigateToMiniProgram in current scene')) {
          errorMessage = onlineStoreConfig.errorMessages.sceneNotSupported;
        } else if (err.errMsg && err.errMsg.includes('appId not exist')) {
          errorMessage = onlineStoreConfig.errorMessages.appNotExist;
        } else if (err.errMsg && err.errMsg.includes('path not exist')) {
          errorMessage = onlineStoreConfig.errorMessages.pathNotExist;
        }

        wx.showModal({
          title: '跳转失败',
          content: errorMessage,
          showCancel: false
        });
      }
    });
  },

  /**
   * 显示联系我们弹窗
   */
  showContact() {
    this.setData({
      showContactModal: true
    })
  },

  /**
   * 关闭联系我们弹窗
   */
  closeContact() {
    this.setData({
      showContactModal: false
    })
  },

  /**
   * 复制联系信息
   */
  copyContact(e) {
    const text = e.currentTarget.dataset.text
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 刷新耗材信息
   * 手动刷新当前耗材信息，用于用户更换耗材后的情况
   */
  refreshMaterialInfo() {
    if (this.data.printerStatus !== 'connected') {
      wx.showToast({
        title: '请先连接打印机',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '获取耗材信息...'
    })

    this.getMaterialInfo().then(() => {
      wx.hideLoading()
      this.performCompatibilityCheck()
      wx.showToast({
        title: '耗材信息已更新',
        icon: 'success'
      })
    }).catch(error => {
      wx.hideLoading()
      console.error('刷新耗材信息失败:', error)
      wx.showToast({
        title: '获取耗材信息失败',
        icon: 'none'
      })
    })
  },

  /**
   * 调试方法：手动检查兼容性
   * 可以在控制台调用 getCurrentPages()[0].debugCheckCompatibility()
   */
  debugCheckCompatibility() {
    console.log('=== 手动兼容性检查 ===')
    console.log('当前状态:', {
      materialMismatch: this.data.materialMismatch,
      forcePrint: this.data.forcePrint,
      printerStatus: this.data.printerStatus,
      selectedTemplateIndex: this.data.selectedTemplateIndex
    })

    if (this.data.printerStatus === 'connected') {
      this.checkMaterialCompatibility(true) // 强制刷新
    } else {
      console.log('打印机未连接，无法检查兼容性')
    }
  }
})
