# 标签打印机预览功能说明

## 功能概述
预览功能允许用户在打印前查看标签的实际效果，支持多页面预览、图片保存等功能。

## 主要功能

### 1. 全局预览
- 点击主界面的"预览"按钮
- 自动从第一页开始预览
- 支持多页面切换

### 2. 单页预览
- 在每个页面配置区域点击"预览此页"按钮
- 直接预览指定页面
- 如果页面没有元素会提示用户

### 3. 预览控制
- **上一页/下一页**: 在多页面时可以切换预览
- **保存图片**: 将当前预览图片保存到手机相册
- **关闭预览**: 关闭预览弹窗

## 使用步骤

1. **添加页面内容**
   - 点击"增加一页"创建新页面
   - 添加文本、条码、二维码等元素
   - 配置页面尺寸和元素属性

2. **预览标签**
   - 点击"预览"按钮或"预览此页"按钮
   - 系统会生成预览图片并显示

3. **查看和操作**
   - 在预览弹窗中查看标签效果
   - 使用上一页/下一页切换（多页面时）
   - 点击"保存图片"保存到相册

## 技术实现

### 核心函数
- `drawPerview(pageIndex)`: 生成指定页面的预览
- `previewAllPages()`: 从第一页开始预览
- `prevPreview()` / `nextPreview()`: 切换预览页面
- `savePreviewImage()`: 保存预览图片到相册

### 数据结构
```javascript
data: {
    showPreview: false,           // 是否显示预览弹窗
    previewImagePath: '',         // 当前预览图片路径
    currentPreviewIndex: 0,       // 当前预览页面索引
    previewImages: []             // 所有页面的预览图片缓存
}
```

### 预览流程
1. 调用 JCAPI.startDrawLabel() 开始绘制
2. 遍历页面元素，调用对应的绘制方法
3. 调用 JCAPI.endDrawLabel() 结束绘制
4. 使用 wx.canvasToTempFilePath() 生成图片
5. 显示预览弹窗并展示图片

## 注意事项

1. **权限要求**: 保存图片到相册需要用户授权
2. **性能优化**: 预览图片会缓存，避免重复生成
3. **错误处理**: 包含完整的错误提示和处理机制
4. **用户体验**: 添加加载提示，提升用户体验

## 样式说明

预览弹窗采用响应式设计：
- 宽度: 90vw，最大800rpx
- 高度: 80vh，最大1200rpx
- 图片容器自适应内容
- 控制按钮支持多种操作

## 扩展功能

未来可以考虑添加：
- 预览图片的缩放功能
- 批量保存所有页面预览
- 预览图片的分享功能
- 预览历史记录
