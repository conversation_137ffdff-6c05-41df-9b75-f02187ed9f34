(function(_0xec3e27,_0x496a9d){const _0x3aecdd=a0_0xc21e,_0x1c1790=_0xec3e27();while(!![]){try{const _0x2337f7=parseInt(_0x3aecdd(0x255))/0x1*(-parseInt(_0x3aecdd(0x1cf))/0x2)+parseInt(_0x3aecdd(0x1ff))/0x3*(-parseInt(_0x3aecdd(0x257))/0x4)+-parseInt(_0x3aecdd(0x1f9))/0x5+-parseInt(_0x3aecdd(0x212))/0x6+parseInt(_0x3aecdd(0x1f1))/0x7*(parseInt(_0x3aecdd(0x238))/0x8)+parseInt(_0x3aecdd(0x1ee))/0x9*(parseInt(_0x3aecdd(0x200))/0xa)+parseInt(_0x3aecdd(0x23b))/0xb;if(_0x2337f7===_0x496a9d)break;else _0x1c1790['push'](_0x1c1790['shift']());}catch(_0x36a12d){_0x1c1790['push'](_0x1c1790['shift']());}}}(a0_0x4fc5,0x35c72));import a0_0x3c2c77 from'./JCDataUntil';function a0_0xc21e(_0x53d83e,_0x111af0){const _0x4fc5de=a0_0x4fc5();return a0_0xc21e=function(_0xc21eb6,_0x59e263){_0xc21eb6=_0xc21eb6-0x1cf;let _0x2c882d=_0x4fc5de[_0xc21eb6];return _0x2c882d;},a0_0xc21e(_0x53d83e,_0x111af0);}import a0_0x431d17 from'./JCAPIErrorCode';function a0_0x4fc5(){const _0x25229b=['getFoundDevices\x20\x20搜索到设备：','platform','5555','getBLEDeviceCharacteristics','agreement','13527nTCDAB','useOutBleListen','connectDeviceFound','15225aGaUXe','debug','-------搜索到设备','getBleAdapterState\x20\x20成功','deviceId','getBLEDeviceServices','setBLEMTU','JCSDK_PLATFORM_DD','636325VQxpWj','available','uuid','printErrorInfoCallback','offBluetoothDeviceFound','setMtuSucc','2613VcrmEq','1990sggOgC','5555BF','offBluetoothAdapterStateChange','ios','scanedPrinters\x20\x20\x20\x20startBleDiscovery','0000E3E5-0000-1000-8000-00805F9B34FB','_notiCharacteristic','ab2hex','---needCmd\x20=\x20','toUpperCase','connected','重复发送了-----2---command=','manager','dealData','characteristicId','getBleAdapterState\x20\x20失败','central','discovering','1298772nehOTx','BleAdapterState:--------------------succ','isSendPictureDataing','--------timeout','sendPrintError','name','bleDevice','printer','onBLEConnectionStateChange','_getFoundDevices','E7810A71-73AE-499D-8C15-FAA9AEF0C3F2','_onValueChange','value','curSendData','endsWith','closeBLEConnection','_getDeviceServices','_createBleConnection','log','clear','errCode','openPrinterCallback','hexToBytes','readC','characteristics','offBLEConnectionStateChange','offBLEConnectionStateChanged','onBLEConnectionStateChanged','openPrinter','services','writeBLECharacteristicValue','sendData','getInfosCallback','_openBleAdapter','sdkPlatform','notifyBLECharacteristicValueChange','---收到数据:','push','64hvefRr','openBluetoothAdapter','reciveDatas','7394816aqFbYS','scanDevice','repeatTimeoutId','AAAA','bleAdapterStateReady','JCSDK_PLATFORM_FS','openPrinterCallbackFaill','startsWith','isEnterpriseWX','stopBluetoothDevicesDiscovery','length','0000E0FF-3C17-D293-8E48-14FE2E4DA212','_stopBleDiscovery','needCMD','0000FFE1-0000-1000-8000-00805F9B34FB','_getBleAdapterState','对象不存在','connectBLEDevice','readBLECharacteristicValue','createData','createBLEConnection','要连接的设备：','_startBleDiscovery','getUint8','BEF8D6C9-9C21-4C9E-B632-BD58C1009F9F','_openBleAdapter\x20\x20成功','9433MHcfbp','----走了失败','1864vateWd','_onBleAdapterStateChange','onBluetoothDeviceFound','onBluetoothAdapterStateChange','bleAdapterSearchState','4tkqkzZ','closeBluetoothAdapter','_openBleAdapter\x20\x20失败','writeC','getBluetoothAdapterState','disconnectBLEDevice','setPrinter','bleConnectionState','FFE1','BleAdapterState:--------------------1','serviceId','onBLECharacteristicValueChange','byteLength','devices','JCSDK_PRINT_ERR_DISCONNECT','适配器状态发送变化:','scanDevicesCallback','android','startBluetoothDevicesDiscovery','发送了：','JCSDK_PLATFORM_WX','getBluetoothDevices','_getFoundPrinterByName','scanedPrinters\x20\x20开始搜索','offBLECharacteristicValueChange','createBleConnection\x20开始连接'];a0_0x4fc5=function(){return _0x25229b;};return a0_0x4fc5();}function Ble(_0x3a69a5){const _0x2bfdee=a0_0xc21e;this[_0x2bfdee(0x23f)]=![],this[_0x2bfdee(0x25b)]=![],this[_0x2bfdee(0x1df)]=null,this[_0x2bfdee(0x23c)]=[],this['manager']=_0x3a69a5,this[_0x2bfdee(0x23a)]='',this[_0x2bfdee(0x258)]();}Ble['prototype']={'constructor':Ble,'scanedPrinters':function(_0x496fad){const _0x1b7044=a0_0xc21e;let _0x3593a9=this;(_0x3593a9['manager'][_0x1b7044(0x1f2)]&0x10000)==0x10000&&console[_0x1b7044(0x224)](_0x1b7044(0x1e6));var _0x3a81ba=function(){_0x496fad!=null&&_0x496fad([]);},_0x40f602=function(){var _0x52f2ff=function(){const _0x1718a1=a0_0xc21e;(_0x3593a9[_0x1718a1(0x20c)][_0x1718a1(0x1f2)]&0x10000)==0x10000&&console[_0x1718a1(0x224)](_0x1718a1(0x204)),_0x3593a9['_startBleDiscovery'](),setTimeout(()=>{const _0x2c8ec5=_0x1718a1;_0x3593a9['_stopBleDiscovery']();let _0x360be3=function(){_0x496fad&&_0x496fad(_0x3593a9['scanDevice']);};_0x3593a9[_0x2c8ec5(0x23c)]=[],_0x3593a9[_0x2c8ec5(0x21b)](_0x360be3);},0xbb8);};_0x3593a9['_getBleAdapterState'](_0x52f2ff,_0x3a81ba);};this[_0x1b7044(0x233)](_0x40f602,_0x3a81ba);},'closePrinter':function(){const _0x4e63bb=a0_0xc21e;if(this[_0x4e63bb(0x218)]!=null&&this[_0x4e63bb(0x218)][_0x4e63bb(0x1f5)]!=null){let _0x24f00e=this;var _0x274c71={'deviceId':_0x24f00e[_0x4e63bb(0x218)]['deviceId'],'complete':()=>{const _0x5efb42=_0x4e63bb;_0x24f00e[_0x5efb42(0x20c)][_0x5efb42(0x219)]=null;}};if(this[_0x4e63bb(0x20c)][_0x4e63bb(0x234)]==a0_0x431d17[_0x4e63bb(0x1f8)])dd[_0x4e63bb(0x1d4)](_0x274c71);else this[_0x4e63bb(0x20c)][_0x4e63bb(0x234)]==a0_0x431d17[_0x4e63bb(0x240)]?tt[_0x4e63bb(0x1d4)](_0x274c71):wx['closeBLEConnection'](_0x274c71);}},'openPrinter':function(_0x15544d,_0xc2b444,_0x55158f){const _0x537f32=a0_0xc21e;let _0x193fcb=this;if(this[_0x537f32(0x1d6)]){this['openPrinterCallbackFaill']&&(this['openPrinterCallbackFaill']=function(){const _0x102b9f=_0x537f32;_0x193fcb['bleDevice']=null,_0x193fcb['manager'][_0x102b9f(0x219)]=null,setTimeout(()=>{const _0x52cc82=_0x102b9f;_0x193fcb[_0x52cc82(0x22e)](_0x15544d,_0xc2b444,_0x55158f);},0x1f4);});if(this['bleDevice']!=null&&this['bleDevice']['deviceId']!=null){if(_0x193fcb[_0x537f32(0x20c)][_0x537f32(0x234)]==a0_0x431d17[_0x537f32(0x1f8)])dd[_0x537f32(0x1d4)]({'deviceId':_0x193fcb['bleDevice'][_0x537f32(0x1f5)]});else _0x193fcb['manager'][_0x537f32(0x234)]==a0_0x431d17['JCSDK_PLATFORM_FS']?tt[_0x537f32(0x1d4)]({'deviceId':_0x193fcb[_0x537f32(0x218)][_0x537f32(0x1f5)]}):wx[_0x537f32(0x221)]({'deviceId':_0x193fcb[_0x537f32(0x218)][_0x537f32(0x1f5)]});}return;}var _0xacc0e7=_0x55158f,_0x4a8a5d=function(){const _0x5e1222=_0x537f32;(_0x193fcb[_0x5e1222(0x20c)]['debug']&0x1)==0x1&&console[_0x5e1222(0x224)](_0x5e1222(0x256));if(_0xacc0e7!=null){let _0x356743=_0xacc0e7;_0xacc0e7=null,_0x356743();}},_0x80a8c9=![],_0x440f0f=![],_0x4552b9=function(){const _0x42c052=_0x537f32;if(_0x80a8c9)return;_0x80a8c9=!![];var _0x35d7d1=function(){const _0x26ca88=a0_0xc21e;_0x440f0f=!![],_0x193fcb[_0x26ca88(0x251)]();var _0x2706d1=setTimeout(()=>{const _0x47afc1=_0x26ca88;(_0x193fcb['manager'][_0x47afc1(0x1f2)]&0x1)==0x1&&console[_0x47afc1(0x224)](_0x47afc1(0x215));_0x193fcb[_0x47afc1(0x247)]();if(_0x193fcb[_0x47afc1(0x1f0)]==![]){_0x4a8a5d();return;}if(_0x440f0f)return;_0x4a8a5d();},0x3a98);let _0xe41fc0=function(_0x185b5f){const _0x566e5f=_0x26ca88;clearTimeout(_0x2706d1);let _0x2cdb8d=_0x185b5f['deviceId'];if(_0x2cdb8d!=null){let _0x40d5b7=![],_0x3f7d43=function(_0x570fcb){const _0x622238=a0_0xc21e;if(_0x40d5b7)return;_0x40d5b7=!![];let _0x3e1d95=function(_0x56ddd7){let _0x383080=function(_0x3d5c94){let _0xe3a365=function(){const _0x36bc42=a0_0xc21e;let _0x5b078a=function(){const _0x544a6f=a0_0xc21e;_0x193fcb[_0x544a6f(0x227)]=_0xc2b444,_0x193fcb[_0x544a6f(0x241)]=_0x55158f,_0x193fcb[_0x544a6f(0x218)]={},_0x193fcb['bleDevice'][_0x544a6f(0x1f5)]=_0x570fcb,_0x193fcb['bleDevice']['serviceId']=_0x56ddd7,_0x193fcb[_0x544a6f(0x218)][_0x544a6f(0x1d2)]=_0x3d5c94,_0x193fcb[_0x544a6f(0x218)][_0x544a6f(0x229)]=_0x3d5c94,_0x193fcb[_0x544a6f(0x218)][_0x544a6f(0x217)]=_0x15544d;if(_0x193fcb[_0x544a6f(0x20c)][_0x544a6f(0x1ef)]){}else _0x193fcb[_0x544a6f(0x21d)]();let _0x52fda4=a0_0x3c2c77[_0x544a6f(0x24e)](0xc1,0x1);_0x52fda4=a0_0x3c2c77['insertAtHeadOfBuffer'](0x3,_0x52fda4),_0x193fcb[_0x544a6f(0x20c)]['needCMD']=0xc2,_0x193fcb['sendData'](_0x52fda4,0x9,()=>{const _0x55b82c=_0x544a6f;_0x193fcb['openPrinterCallbackFaill']&&(_0x193fcb[_0x55b82c(0x241)]=function(){const _0x33e454=_0x55b82c;_0x193fcb[_0x33e454(0x218)]=null,_0x193fcb[_0x33e454(0x20c)][_0x33e454(0x219)]=null,_0x4a8a5d();});if(_0x193fcb[_0x55b82c(0x20c)][_0x55b82c(0x234)]==a0_0x431d17[_0x55b82c(0x1f8)])dd[_0x55b82c(0x221)]({'deviceId':_0x193fcb['bleDevice']['deviceId']});else _0x193fcb['manager'][_0x55b82c(0x234)]==a0_0x431d17['JCSDK_PLATFORM_FS']?tt['disconnectBLEDevice']({'deviceId':_0x193fcb[_0x55b82c(0x218)]['deviceId']}):wx[_0x55b82c(0x221)]({'deviceId':_0x193fcb[_0x55b82c(0x218)]['deviceId']});});};_0x193fcb[_0x36bc42(0x206)](_0x570fcb,_0x56ddd7,_0x3d5c94,_0x5b078a,_0x4a8a5d);};_0xe3a365();};_0x193fcb['_getDeviceCharacteristics'](_0x570fcb,_0x56ddd7,_0x383080,_0x4a8a5d);};_0x193fcb[_0x622238(0x222)](_0x570fcb,_0x3e1d95,_0x4a8a5d);};_0x193fcb['_onConnectionStateChange'](_0x3f7d43);if(_0x193fcb[_0x566e5f(0x20c)]['platform']==_0x566e5f(0x203))_0x193fcb[_0x566e5f(0x223)](_0x2cdb8d,_0x4a8a5d);else{var _0x5251ca=0x0;let _0x537f2f=function(){_0x5251ca++,setTimeout(()=>{const _0x16e465=a0_0xc21e;_0x5251ca<0x3?_0x193fcb[_0x16e465(0x223)](_0x2cdb8d,_0x537f2f):_0x193fcb[_0x16e465(0x223)](_0x2cdb8d,_0x4a8a5d);},0x3e8);};_0x537f2f();}}else _0x4a8a5d();};_0x193fcb[_0x26ca88(0x1e5)](_0x15544d,_0xe41fc0);};_0x193fcb[_0x42c052(0x24a)](_0x35d7d1,_0x4a8a5d);};this['_openBleAdapter'](_0x4552b9,_0x4a8a5d);},'sendData':function(_0x7957c1,_0x388609=0xa,_0x4312e7){const _0x4e4e09=a0_0xc21e;if(this[_0x4e4e09(0x218)]==null||this[_0x4e4e09(0x218)]===undefined||this[_0x4e4e09(0x218)]['deviceId']===undefined){(this[_0x4e4e09(0x20c)][_0x4e4e09(0x1f2)]&0x1)==0x1&&console['log'](_0x4e4e09(0x24b));this[_0x4e4e09(0x23d)]&&this[_0x4e4e09(0x23d)]>0x0&&(clearTimeout(this['repeatTimeoutId']),this[_0x4e4e09(0x23d)]=0x0);return;}var _0x2da8fa=this;function _0x5e6a67(){const _0x3c1543=_0x4e4e09;_0x388609>0x0?_0x2da8fa[_0x3c1543(0x23d)]=setTimeout(()=>{const _0x3e3b64=_0x3c1543;if(_0x2da8fa[_0x3e3b64(0x20c)][_0x3e3b64(0x219)]&&_0x2da8fa[_0x3e3b64(0x20c)]['printer'][_0x3e3b64(0x214)])return;if(_0x2da8fa[_0x3e3b64(0x20c)][_0x3e3b64(0x21f)]==null)return;const _0x538574=new DataView(_0x2da8fa[_0x3e3b64(0x20c)][_0x3e3b64(0x21f)]);if(_0x2da8fa[_0x3e3b64(0x20c)]['curSendData']&&_0x2da8fa['manager']['curSendData'][_0x3e3b64(0x1db)]>0x5){let _0x433228=_0x538574[_0x3e3b64(0x252)](0x2);(_0x2da8fa[_0x3e3b64(0x20c)][_0x3e3b64(0x1f2)]&0x100)==0x100&&console[_0x3e3b64(0x224)](_0x3e3b64(0x20b)+_0x433228+'---bytelength='+_0x2da8fa[_0x3e3b64(0x20c)]['curSendData']['byteLength']+_0x3e3b64(0x208)+_0x2da8fa[_0x3e3b64(0x20c)][_0x3e3b64(0x248)]);if(_0x433228==0x86&&_0x2da8fa[_0x3e3b64(0x20c)][_0x3e3b64(0x248)]!=0xd3)return;else{if(_0x433228==0x13&&_0x2da8fa[_0x3e3b64(0x20c)][_0x3e3b64(0x248)]!=0x14)return;else{if(_0x433228==0xf3&&_0x2da8fa[_0x3e3b64(0x20c)]['needCMD']!=0xf4)return;else{if(_0x433228==0xa3&&_0x2da8fa[_0x3e3b64(0x20c)][_0x3e3b64(0x248)]!=0xb3)return;}}}}_0x2da8fa[_0x3e3b64(0x231)](_0x2da8fa[_0x3e3b64(0x20c)]['curSendData'],--_0x388609,_0x4312e7);},0x1f4):(_0x2da8fa[_0x3c1543(0x20c)][_0x3c1543(0x21f)]=null,_0x4312e7&&_0x4312e7());}if(_0x2da8fa[_0x4e4e09(0x20c)][_0x4e4e09(0x234)]==a0_0x431d17[_0x4e4e09(0x1f8)])(this['manager']['debug']&0x100)==0x100&&console[_0x4e4e09(0x224)](_0x4e4e09(0x1e2)+a0_0x3c2c77['ab2hex'](_0x7957c1)),this[_0x4e4e09(0x20c)][_0x4e4e09(0x21f)]=_0x7957c1,dd[_0x4e4e09(0x230)]({'characteristicId':this[_0x4e4e09(0x218)][_0x4e4e09(0x1d2)],'deviceId':this[_0x4e4e09(0x218)][_0x4e4e09(0x1f5)],'serviceId':this[_0x4e4e09(0x218)][_0x4e4e09(0x1d9)],'value':_0x7957c1,'success':_0x4f6808=>{},'fail':_0x48a764=>{},'complete':()=>{_0x5e6a67();}});else{if(_0x2da8fa['manager'][_0x4e4e09(0x234)]==a0_0x431d17[_0x4e4e09(0x240)]){var _0x397062=a0_0x3c2c77[_0x4e4e09(0x207)](_0x7957c1);(this['manager']['debug']&0x100)==0x100&&console[_0x4e4e09(0x224)](_0x397062),this[_0x4e4e09(0x20c)][_0x4e4e09(0x21f)]=_0x7957c1,tt[_0x4e4e09(0x230)]({'characteristicId':this[_0x4e4e09(0x218)][_0x4e4e09(0x1d2)],'deviceId':this[_0x4e4e09(0x218)]['deviceId'],'serviceId':this[_0x4e4e09(0x218)]['serviceId'],'value':_0x397062,'success':_0x55d1fa=>{_0x5e6a67();},'fail':_0x8288e3=>{}});}else(this[_0x4e4e09(0x20c)][_0x4e4e09(0x1f2)]&0x100)==0x100&&console[_0x4e4e09(0x224)](_0x4e4e09(0x1e2)+a0_0x3c2c77['ab2hex'](_0x7957c1)),this[_0x4e4e09(0x20c)][_0x4e4e09(0x21f)]=_0x7957c1,wx[_0x4e4e09(0x230)]({'characteristicId':this[_0x4e4e09(0x218)]['writeC'],'deviceId':this[_0x4e4e09(0x218)][_0x4e4e09(0x1f5)],'serviceId':this[_0x4e4e09(0x218)][_0x4e4e09(0x1d9)],'value':_0x7957c1,'writeType':'writeNoResponse','success':_0x14bf42=>{},'fail':_0x29bf94=>{},'complete':()=>{_0x5e6a67();}});}},'clearRepeatAction':function(){const _0x24aa9c=a0_0xc21e;this[_0x24aa9c(0x23d)]>0x0&&(clearTimeout(this[_0x24aa9c(0x23d)]),this[_0x24aa9c(0x23d)]=0x0);},'_getFoundPrinterByName':function(_0xbd792,_0x2c486c){const _0x4964d6=a0_0xc21e;let _0x4b3b83=this;var _0x3922c1=![];this[_0x4964d6(0x1f0)]=![];(_0x4b3b83[_0x4964d6(0x20c)]['debug']&0x10000)==0x10000&&console[_0x4964d6(0x224)](_0x4964d6(0x250)+_0xbd792);if(this['manager'][_0x4964d6(0x234)]==a0_0x431d17[_0x4964d6(0x1e3)]&&this[_0x4964d6(0x20c)][_0x4964d6(0x243)]==!![]){this['scanedPrinters'](_0x20f2d4=>{const _0x17d303=_0x4964d6;for(let _0x3b2e5d=0x0;_0x3b2e5d<_0x20f2d4['length'];_0x3b2e5d++){const _0x2586d9=_0x20f2d4[_0x3b2e5d];if(_0x2586d9[_0x17d303(0x217)]==_0xbd792){_0x4b3b83[_0x17d303(0x1f0)]=!![];if(_0x2c486c){_0x4b3b83[_0x17d303(0x247)]();(_0x4b3b83[_0x17d303(0x20c)]['debug']&0x10000)==0x10000&&console['log']('getFoundPrinterByName\x20搜索到设备去连接');setTimeout(()=>{_0x2c486c(_0x2586d9);},0x1f4);break;}}}});return;}this[_0x4964d6(0x1f0)]=![];var _0x3f2dd9=function(_0x57bc4a){const _0x2737a1=_0x4964d6;if(_0x3922c1)return;if(_0x57bc4a['devices'][_0x2737a1(0x245)]>0x0){var _0x3cdc99=_0x57bc4a[_0x2737a1(0x1dc)][0x0];_0x3cdc99['name']==_0xbd792&&(_0x3922c1=!![],_0x4b3b83[_0x2737a1(0x1f0)]=!![],_0x2c486c&&(_0x4b3b83['_stopBleDiscovery'](),(_0x4b3b83[_0x2737a1(0x20c)][_0x2737a1(0x1f2)]&0x1)==0x1&&console['log'](_0x2737a1(0x1f3)),_0x2c486c(_0x3cdc99)));}};if(_0x4b3b83[_0x4964d6(0x20c)]['sdkPlatform']==a0_0x431d17[_0x4964d6(0x1f8)])dd[_0x4964d6(0x1fd)](),dd[_0x4964d6(0x259)](_0x3f2dd9);else _0x4b3b83[_0x4964d6(0x20c)][_0x4964d6(0x234)]==a0_0x431d17[_0x4964d6(0x240)]?(tt['offBluetoothDeviceFound'](),tt['onBluetoothDeviceFound'](_0x3f2dd9)):(wx[_0x4964d6(0x1fd)](),wx[_0x4964d6(0x259)](_0x3f2dd9));},'_onValueChange':function(){const _0x1f0b1f=a0_0xc21e;let _0x274525=this;var _0x592144=function(_0x8e9d2b){const _0x50d064=a0_0xc21e;(_0x274525[_0x50d064(0x20c)][_0x50d064(0x1f2)]&0x1000)==0x1000&&console['log'](_0x8e9d2b[_0x50d064(0x21e)]);if(_0x274525['reciveDatas'][_0x50d064(0x245)]>0x0){if(_0x8e9d2b[_0x50d064(0x21e)][_0x50d064(0x209)]()[_0x50d064(0x220)](_0x50d064(0x23e))){var _0x5c9d5b=_0x274525[_0x50d064(0x23a)]+_0x8e9d2b['value'];_0x274525[_0x50d064(0x23a)]='',a0_0x3c2c77[_0x50d064(0x20d)](a0_0x3c2c77[_0x50d064(0x228)](_0x5c9d5b),_0x274525[_0x50d064(0x20c)]);}else _0x274525[_0x50d064(0x23a)]=_0x274525[_0x50d064(0x23a)]+_0x8e9d2b[_0x50d064(0x21e)];}else{if(_0x8e9d2b[_0x50d064(0x21e)]['startsWith'](_0x50d064(0x1eb))&&_0x8e9d2b[_0x50d064(0x21e)][_0x50d064(0x209)]()['endsWith'](_0x50d064(0x23e)))_0x274525[_0x50d064(0x20c)][_0x50d064(0x234)]==a0_0x431d17['JCSDK_PLATFORM_FS']&&_0x274525['manager']['setMtuSucc']==![]&&(_0x8e9d2b['value']['startsWith'](_0x50d064(0x201))&&_0x8e9d2b[_0x50d064(0x21e)][_0x50d064(0x209)]()[_0x50d064(0x220)](_0x50d064(0x23e))&&_0x8e9d2b[_0x50d064(0x21e)][_0x50d064(0x245)]>0x32&&(_0x274525[_0x50d064(0x20c)][_0x50d064(0x1fe)]=!![])),_0x274525[_0x50d064(0x23a)]='',a0_0x3c2c77[_0x50d064(0x20d)](a0_0x3c2c77['hexToBytes'](_0x8e9d2b[_0x50d064(0x21e)]),_0x274525[_0x50d064(0x20c)]);else{if(_0x274525[_0x50d064(0x20c)]['sdkPlatform']==a0_0x431d17[_0x50d064(0x240)]&&_0x8e9d2b[_0x50d064(0x21e)][_0x50d064(0x242)](_0x50d064(0x1eb))==![]){(_0x274525[_0x50d064(0x20c)]['debug']&0x1000)==0x1000&&console[_0x50d064(0x224)]('not\x20command\x20data');return;}_0x274525[_0x50d064(0x23a)]=_0x8e9d2b['value'];}}};if(_0x274525[_0x1f0b1f(0x20c)][_0x1f0b1f(0x234)]==a0_0x431d17[_0x1f0b1f(0x1f8)])dd[_0x1f0b1f(0x1e7)](),dd[_0x1f0b1f(0x1da)](_0x592144);else _0x274525[_0x1f0b1f(0x20c)][_0x1f0b1f(0x234)]==a0_0x431d17[_0x1f0b1f(0x240)]?(tt[_0x1f0b1f(0x1e7)](),tt[_0x1f0b1f(0x1da)](_0x592144)):wx[_0x1f0b1f(0x1da)](_0x64c42b=>{const _0x4272d1=_0x1f0b1f;(_0x274525[_0x4272d1(0x20c)][_0x4272d1(0x1f2)]&0x1000)==0x1000&&console[_0x4272d1(0x224)](_0x4272d1(0x236)+a0_0x3c2c77[_0x4272d1(0x207)](_0x64c42b[_0x4272d1(0x21e)])),a0_0x3c2c77['dealData'](_0x64c42b[_0x4272d1(0x21e)],_0x274525[_0x4272d1(0x20c)]);});},'onOutValueChange':function(_0x292f9e){const _0x3d074e=a0_0xc21e;(_0x292f9e[_0x3d074e(0x20e)][_0x3d074e(0x209)]()=='BEF8D6C9-9C21-4C9E-B632-BD58C1009F9F'||_0x292f9e['characteristicId']['toUpperCase']()==_0x3d074e(0x1d7)||_0x292f9e['characteristicId']['toUpperCase']()==_0x3d074e(0x249))&&((this[_0x3d074e(0x20c)]['debug']&0x1000)==0x1000&&console['log'](_0x3d074e(0x236)+a0_0x3c2c77[_0x3d074e(0x207)](_0x292f9e[_0x3d074e(0x21e)])),a0_0x3c2c77[_0x3d074e(0x20d)](_0x292f9e['value'],this[_0x3d074e(0x20c)]));},'_notiCharacteristic':function(_0x51aabc,_0xa9b0fa,_0x20d267,_0x5bacce,_0x385260){const _0x3d4a4c=a0_0xc21e;let _0x1db7d6=this;var _0x4de6d5={'deviceId':_0x51aabc,'serviceId':_0xa9b0fa,'characteristicId':_0x20d267,'state':!![],'success':_0x47c650=>{_0x5bacce&&_0x5bacce();},'fail':_0x4f3f5b=>{_0x385260&&_0x385260();}};if(_0x1db7d6['manager'][_0x3d4a4c(0x234)]==a0_0x431d17['JCSDK_PLATFORM_DD'])dd[_0x3d4a4c(0x235)](_0x4de6d5);else _0x1db7d6[_0x3d4a4c(0x20c)][_0x3d4a4c(0x234)]==a0_0x431d17[_0x3d4a4c(0x240)]?tt[_0x3d4a4c(0x235)](_0x4de6d5):wx['notifyBLECharacteristicValueChange'](_0x4de6d5);},'_readCharacteristic':function(_0x215bc4,_0x333cf6,_0x16ece3,_0x1d5f2a,_0x59d6a9){const _0x5b4a31=a0_0xc21e;var _0xd7ed5f={'characteristicId':_0x16ece3,'deviceId':_0x215bc4,'serviceId':_0x333cf6,'success':_0x492200=>{_0x1d5f2a&&_0x1d5f2a();},'fail':_0x274c09=>{_0x59d6a9&&_0x59d6a9();}};if(self[_0x5b4a31(0x20c)][_0x5b4a31(0x234)]==a0_0x431d17['JCSDK_PLATFORM_DD'])dd[_0x5b4a31(0x24d)](_0xd7ed5f);else self['manager'][_0x5b4a31(0x234)]==a0_0x431d17[_0x5b4a31(0x240)]?tt[_0x5b4a31(0x24d)](_0xd7ed5f):wx[_0x5b4a31(0x24d)](_0xd7ed5f);},'_getDeviceCharacteristics':function(_0x393631,_0xb7dafe,_0x4992e3,_0x5a5ae6){const _0x5dc05e=a0_0xc21e;let _0x3815be=this;var _0x4fa842=function(_0x266427){const _0x189999=a0_0xc21e;var _0x286c07=![];for(var _0x1d566c in _0x266427[_0x189999(0x22a)]){var _0x41b3da=_0x266427[_0x189999(0x22a)][_0x1d566c];if(_0x41b3da['uuid'][_0x189999(0x209)]()==_0x189999(0x253)||_0x41b3da['uuid'][_0x189999(0x209)]()==_0x189999(0x1d7)||_0x41b3da[_0x189999(0x1fb)][_0x189999(0x209)]()=='0000FFE1-0000-1000-8000-00805F9B34FB'){_0x286c07=!![];_0x4992e3&&_0x4992e3(_0x41b3da[_0x189999(0x1fb)]);break;}}!_0x286c07&&_0x5a5ae6&&_0x5a5ae6();};if(_0x3815be[_0x5dc05e(0x20c)][_0x5dc05e(0x234)]==a0_0x431d17[_0x5dc05e(0x1f8)])_0x4fa842=function(_0x416eb2){const _0x3ae12d=_0x5dc05e;var _0x5315e7=![];for(var _0x4d4a0f in _0x416eb2[_0x3ae12d(0x22a)]){var _0x24f87b=_0x416eb2[_0x3ae12d(0x22a)][_0x4d4a0f];if(_0x24f87b[_0x3ae12d(0x20e)][_0x3ae12d(0x209)]()==_0x3ae12d(0x253)||_0x24f87b[_0x3ae12d(0x20e)][_0x3ae12d(0x209)]()==_0x3ae12d(0x1d7)||_0x24f87b[_0x3ae12d(0x20e)][_0x3ae12d(0x209)]()==_0x3ae12d(0x249)){_0x5315e7=!![];_0x4992e3&&_0x4992e3(_0x24f87b['characteristicId']);break;}}!_0x5315e7&&_0x5a5ae6&&_0x5a5ae6();},dd[_0x5dc05e(0x1ec)]({'deviceId':_0x393631,'serviceId':_0xb7dafe,'success':_0x4fa842,'fail':_0x50cd05=>{_0x5a5ae6&&_0x5a5ae6();}});else _0x3815be[_0x5dc05e(0x20c)]['sdkPlatform']==a0_0x431d17[_0x5dc05e(0x240)]?(_0x4fa842=function(_0x462fc2){const _0x56c8d1=_0x5dc05e;var _0x412213=![];for(var _0x37d652 in _0x462fc2[_0x56c8d1(0x22a)]){var _0x302124=_0x462fc2[_0x56c8d1(0x22a)][_0x37d652];if(_0x302124[_0x56c8d1(0x20e)][_0x56c8d1(0x209)]()==_0x56c8d1(0x253)||_0x302124[_0x56c8d1(0x20e)][_0x56c8d1(0x209)]()=='FFE1'||_0x302124[_0x56c8d1(0x20e)]['toUpperCase']()=='0000FFE1-0000-1000-8000-00805F9B34FB'){_0x412213=!![];_0x4992e3&&_0x4992e3(_0x302124[_0x56c8d1(0x20e)]);break;}}!_0x412213&&_0x5a5ae6&&_0x5a5ae6();},tt[_0x5dc05e(0x1ec)]({'deviceId':_0x393631,'serviceId':_0xb7dafe,'success':_0x4fa842,'fail':_0x36b4c0=>{_0x5a5ae6&&_0x5a5ae6();}})):wx[_0x5dc05e(0x1ec)]({'deviceId':_0x393631,'serviceId':_0xb7dafe,'success':_0x4fa842,'fail':_0x4d8eae=>{_0x5a5ae6&&_0x5a5ae6();}});},'_getDeviceServices':function(_0x5b477e,_0x5e285a,_0x1e885f){const _0x4e94ac=a0_0xc21e;let _0x53a2f2=this;var _0x5c2d4=function(_0x4972eb){const _0x3b4afb=a0_0xc21e;let _0x2df215=![];for(let _0x5880be in _0x4972eb[_0x3b4afb(0x22f)]){let _0x52d2e9=_0x4972eb['services'][_0x5880be];if(_0x52d2e9[_0x3b4afb(0x1fb)][_0x3b4afb(0x209)]()==_0x3b4afb(0x21c)||_0x52d2e9[_0x3b4afb(0x1fb)][_0x3b4afb(0x209)]()=='0000E0FF-3C17-D293-8E48-14FE2E4DA212'||_0x52d2e9[_0x3b4afb(0x1fb)][_0x3b4afb(0x209)]()==_0x3b4afb(0x205)){_0x5e285a&&_0x5e285a(_0x52d2e9['uuid']);_0x2df215=!![];break;}}!_0x2df215&&_0x1e885f&&_0x1e885f();},_0x1c81dd=function(_0x35d0b8){_0x1e885f&&_0x1e885f();};if(_0x53a2f2[_0x4e94ac(0x20c)][_0x4e94ac(0x234)]==a0_0x431d17[_0x4e94ac(0x1f8)])_0x5c2d4=function(_0x516f00){const _0x244ee3=_0x4e94ac;let _0x529719=![];for(let _0x5dc542 in _0x516f00[_0x244ee3(0x22f)]){let _0x5663b5=_0x516f00['services'][_0x5dc542];console[_0x244ee3(0x224)](_0x5663b5[_0x244ee3(0x1d9)]);if(_0x5663b5[_0x244ee3(0x1d9)][_0x244ee3(0x209)]()=='E7810A71-73AE-499D-8C15-FAA9AEF0C3F2'||_0x5663b5[_0x244ee3(0x1d9)][_0x244ee3(0x209)]()==_0x244ee3(0x246)||_0x5663b5['serviceId']['toUpperCase']()==_0x244ee3(0x205)){_0x5e285a&&_0x5e285a(_0x5663b5['serviceId']);_0x529719=!![];break;}}!_0x529719&&_0x1e885f&&_0x1e885f();},dd[_0x4e94ac(0x1f6)]({'deviceId':_0x5b477e,'success':_0x5c2d4,'fail':_0x1c81dd});else _0x53a2f2[_0x4e94ac(0x20c)][_0x4e94ac(0x234)]==a0_0x431d17[_0x4e94ac(0x240)]?(_0x5c2d4=function(_0x2cc913){const _0x4c543e=_0x4e94ac;let _0x31e06b=![];for(let _0x116e78 in _0x2cc913[_0x4c543e(0x22f)]){let _0x509c0e=_0x2cc913[_0x4c543e(0x22f)][_0x116e78];if(_0x509c0e[_0x4c543e(0x1d9)][_0x4c543e(0x209)]()==_0x4c543e(0x21c)||_0x509c0e[_0x4c543e(0x1d9)][_0x4c543e(0x209)]()==_0x4c543e(0x246)||_0x509c0e[_0x4c543e(0x1d9)]['toUpperCase']()==_0x4c543e(0x205)){_0x5e285a&&_0x5e285a(_0x509c0e[_0x4c543e(0x1d9)]);_0x31e06b=!![];break;}}!_0x31e06b&&_0x1e885f&&_0x1e885f();},tt[_0x4e94ac(0x1f6)]({'deviceId':_0x5b477e,'success':_0x5c2d4,'fail':_0x1c81dd})):wx[_0x4e94ac(0x1f6)]({'deviceId':_0x5b477e,'success':_0x5c2d4,'fail':_0x1c81dd});},'_onConnectionStateChange':function(_0x3cc21c,_0x3661d4){const _0x3f027d=a0_0xc21e;let _0x14bc80=this;if(_0x14bc80[_0x3f027d(0x20c)][_0x3f027d(0x234)]==a0_0x431d17[_0x3f027d(0x1f8)])dd[_0x3f027d(0x22d)](_0x1db253=>{const _0x3ae233=_0x3f027d;(_0x14bc80['manager']['debug']&0x1)==0x1&&console[_0x3ae233(0x224)](_0x1db253),_0x14bc80[_0x3ae233(0x1d6)]=_0x1db253[_0x3ae233(0x20a)],_0x1db253[_0x3ae233(0x20a)]?_0x3cc21c&&_0x3cc21c(_0x1db253['deviceId']):(dd[_0x3ae233(0x1e7)](),dd[_0x3ae233(0x22c)](),_0x14bc80[_0x3ae233(0x20c)]&&_0x14bc80[_0x3ae233(0x20c)]['clear'](),_0x14bc80['repeatTimeoutId']>0x0&&clearTimeout(_0x14bc80['repeatTimeoutId']),_0x14bc80[_0x3ae233(0x218)]!=null&&_0x14bc80[_0x3ae233(0x218)][_0x3ae233(0x1f5)]!=null&&(_0x14bc80[_0x3ae233(0x218)][_0x3ae233(0x1f5)]=null,_0x14bc80[_0x3ae233(0x218)]=null,_0x14bc80[_0x3ae233(0x241)]&&_0x14bc80[_0x3ae233(0x241)](),dd[_0x3ae233(0x22c)]()),_0x3661d4&&_0x3661d4());},_0x2186d1=>{const _0x51f2dd=_0x3f027d;(_0x14bc80[_0x51f2dd(0x20c)]['debug']&0x1)==0x1&&console['log'](_0x2186d1);});else _0x14bc80['manager'][_0x3f027d(0x234)]==a0_0x431d17['JCSDK_PLATFORM_FS']?(tt['offBLEConnectionStateChange'](),tt[_0x3f027d(0x21a)](_0x4da691=>{const _0x3ba9a2=_0x3f027d;(_0x14bc80[_0x3ba9a2(0x20c)][_0x3ba9a2(0x1f2)]&0x1)==0x1&&console[_0x3ba9a2(0x224)](_0x4da691),_0x14bc80[_0x3ba9a2(0x1d6)]=_0x4da691['connected'],_0x4da691['connected']?_0x3cc21c&&_0x3cc21c(_0x4da691[_0x3ba9a2(0x1f5)]):(_0x14bc80['manager']&&_0x14bc80[_0x3ba9a2(0x20c)][_0x3ba9a2(0x225)](),_0x14bc80[_0x3ba9a2(0x23d)]>0x0&&clearTimeout(_0x14bc80['repeatTimeoutId']),_0x14bc80[_0x3ba9a2(0x218)]!=null&&_0x14bc80[_0x3ba9a2(0x218)][_0x3ba9a2(0x1f5)]!=null&&(_0x14bc80[_0x3ba9a2(0x218)][_0x3ba9a2(0x1f5)]=null,_0x14bc80[_0x3ba9a2(0x218)]=null,_0x14bc80[_0x3ba9a2(0x241)]&&_0x14bc80['openPrinterCallbackFaill'](),tt[_0x3ba9a2(0x22b)](),tt[_0x3ba9a2(0x1fd)](),tt['offBluetoothAdapterStateChange'](),tt[_0x3ba9a2(0x1d0)]()),_0x3661d4&&_0x3661d4());},_0x36d654=>{const _0x3f49a2=_0x3f027d;(_0x14bc80[_0x3f49a2(0x20c)][_0x3f49a2(0x1f2)]&0x1)==0x1&&console[_0x3f49a2(0x224)](_0x36d654);})):(wx['offBLEConnectionStateChange'](),wx[_0x3f027d(0x21a)](_0x1b6c4b=>{const _0x4ada30=_0x3f027d;(_0x14bc80[_0x4ada30(0x20c)][_0x4ada30(0x1f2)]&0x1)==0x1&&console['log'](_0x1b6c4b),(_0x14bc80[_0x4ada30(0x20c)][_0x4ada30(0x1f2)]&0x10000)==0x10000&&console['log']('蓝牙连接状态'+_0x1b6c4b[_0x4ada30(0x20a)]),_0x14bc80['bleConnectionState']=_0x1b6c4b['connected'],_0x1b6c4b[_0x4ada30(0x20a)]?((_0x14bc80[_0x4ada30(0x20c)][_0x4ada30(0x1f2)]&0x10000)==0x10000&&console[_0x4ada30(0x224)]('蓝牙连接成功。。。'),_0x3cc21c&&_0x3cc21c(_0x1b6c4b['deviceId'])):(_0x14bc80[_0x4ada30(0x20c)]&&_0x14bc80['manager'][_0x4ada30(0x225)](),_0x14bc80['repeatTimeoutId']>0x0&&clearTimeout(_0x14bc80['repeatTimeoutId']),_0x14bc80[_0x4ada30(0x218)]!=null&&_0x14bc80[_0x4ada30(0x218)][_0x4ada30(0x1f5)]!=null&&(_0x14bc80[_0x4ada30(0x218)][_0x4ada30(0x1f5)]=null,_0x14bc80[_0x4ada30(0x218)]=null,_0x14bc80[_0x4ada30(0x241)]&&_0x14bc80[_0x4ada30(0x241)](),wx['offBLEConnectionStateChange'](),wx[_0x4ada30(0x1fd)](),wx[_0x4ada30(0x202)](),wx['closeBluetoothAdapter']()),_0x3661d4&&_0x3661d4());},_0x3c1bd7=>{const _0x330641=_0x3f027d;(_0x14bc80[_0x330641(0x20c)]['debug']&0x1)==0x1&&console[_0x330641(0x224)](_0x3c1bd7);}));},'_createBleConnection':function(_0x4a3c19,_0x38617c){const _0x1d4fba=a0_0xc21e;let _0x3866f6=this;var _0x3d30bc=function(_0x4872d3){const _0x2e826a=a0_0xc21e;(_0x3866f6[_0x2e826a(0x20c)]['debug']&0x1)==0x1&&console[_0x2e826a(0x224)](_0x4872d3);if(_0x3866f6[_0x2e826a(0x20c)]['sdkPlatform']==a0_0x431d17[_0x2e826a(0x1e3)]){if(_0x4872d3[_0x2e826a(0x226)]==-0x1){wx['closeBLEConnection']({'deviceId':_0x4a3c19,'complete':()=>{_0x38617c&&_0x38617c();}});return;}}_0x38617c&&_0x38617c();};if(_0x3866f6[_0x1d4fba(0x20c)][_0x1d4fba(0x234)]==a0_0x431d17[_0x1d4fba(0x1f8)])dd[_0x1d4fba(0x24c)]({'deviceId':_0x4a3c19,'success':_0x4b2bb5=>{const _0x3bd3a7=_0x1d4fba;if(_0x3866f6[_0x3bd3a7(0x20c)][_0x3bd3a7(0x1ea)]==_0x3bd3a7(0x1e0)){}},'fail':_0x3d30bc,'complete':()=>{}});else _0x3866f6[_0x1d4fba(0x20c)][_0x1d4fba(0x234)]==a0_0x431d17[_0x1d4fba(0x240)]?tt['connectBLEDevice']({'deviceId':_0x4a3c19,'success':_0x4d7a8c=>{const _0x1fe34b=_0x1d4fba;_0x3866f6['manager']['platform']==_0x1fe34b(0x1e0)&&(_0x3866f6[_0x1fe34b(0x20c)][_0x1fe34b(0x1fe)]=![],tt[_0x1fe34b(0x1f7)]({'deviceId':_0x4a3c19,'mtu':0xdf,'success'(_0x8c0514){const _0x277bb2=_0x1fe34b;_0x3866f6[_0x277bb2(0x20c)][_0x277bb2(0x1fe)]=!![];},'fail'(_0x5b8397){const _0x29aea3=_0x1fe34b;_0x3866f6[_0x29aea3(0x20c)][_0x29aea3(0x1fe)]=![];}}));},'fail':_0x3d30bc,'complete':()=>{}}):((_0x3866f6[_0x1d4fba(0x20c)][_0x1d4fba(0x1f2)]&0x10000)==0x10000&&console['log'](_0x1d4fba(0x1e8)),wx[_0x1d4fba(0x24f)]({'deviceId':_0x4a3c19,'success':_0x3e8d67=>{const _0x4646a0=_0x1d4fba;_0x3866f6[_0x4646a0(0x20c)][_0x4646a0(0x1ea)]==_0x4646a0(0x1e0)&&wx[_0x4646a0(0x1f7)]({'deviceId':_0x4a3c19,'mtu':0xf0});},'fail':_0x3d30bc,'complete':()=>{}}));},'_containDevice':function(_0xb01c53){const _0x358238=a0_0xc21e;let _0x341681=null;(this['manager'][_0x358238(0x1f2)]&0x1)==0x1&&console['log'](this['scanDevice']);for(let _0x3f28a0=0x0;_0x3f28a0<this[_0x358238(0x23c)][_0x358238(0x245)];_0x3f28a0++){let _0x450517=this[_0x358238(0x23c)][_0x3f28a0];if(_0x450517[_0x358238(0x217)]==_0xb01c53){_0x341681=_0x450517[_0x358238(0x1f5)];break;}}return _0x341681;},'_getFoundDevices':function(_0x183359){const _0x392b23=a0_0xc21e;let _0x13e6d0=this;var _0xbca11d=function(_0x257317){var _0x584287=_0x257317['devices'];_0x584287['forEach'](_0x1a6849=>{const _0x3b85ad=a0_0xc21e;(_0x13e6d0[_0x3b85ad(0x20c)][_0x3b85ad(0x1f2)]&0x10000)==0x10000&&console['log'](_0x3b85ad(0x1e9)+_0x1a6849[_0x3b85ad(0x217)]),_0x13e6d0[_0x3b85ad(0x23c)][_0x3b85ad(0x237)](_0x1a6849);}),_0x183359&&_0x183359();};if(_0x13e6d0[_0x392b23(0x20c)]['sdkPlatform']==a0_0x431d17[_0x392b23(0x1f8)])dd[_0x392b23(0x1e4)]({'success':_0xbca11d});else _0x13e6d0[_0x392b23(0x20c)]['sdkPlatform']==a0_0x431d17[_0x392b23(0x240)]?tt[_0x392b23(0x1e4)]({'success':_0xbca11d}):wx['getBluetoothDevices']({'success':_0xbca11d});},'_stopBleDiscovery':function(){const _0x56838c=a0_0xc21e;let _0x1828a=this;if(_0x1828a[_0x56838c(0x20c)]['sdkPlatform']==a0_0x431d17['JCSDK_PLATFORM_DD'])dd[_0x56838c(0x244)]({'success':()=>{},'fail':()=>{},'complete':()=>{}});else _0x1828a[_0x56838c(0x20c)][_0x56838c(0x234)]==a0_0x431d17['JCSDK_PLATFORM_FS']?tt[_0x56838c(0x244)]({'complete':()=>{}}):wx['stopBluetoothDevicesDiscovery']({'complete':()=>{}});},'_startBleDiscovery':function(){const _0x205c74=a0_0xc21e;let _0x578912=this;if(_0x578912[_0x205c74(0x20c)][_0x205c74(0x234)]==a0_0x431d17['JCSDK_PLATFORM_DD'])dd[_0x205c74(0x1e1)]({'allowDuplicatesKey':!![]});else _0x578912[_0x205c74(0x20c)][_0x205c74(0x234)]==a0_0x431d17[_0x205c74(0x240)]?tt['startBluetoothDevicesDiscovery']({'allowDuplicatesKey':!![]}):wx[_0x205c74(0x1e1)]({'allowDuplicatesKey':!![]});},'_getBleAdapterState':function(_0x2ad541,_0xe7fa03){const _0x4dc739=a0_0xc21e;let _0x39cf04=this;(this[_0x4dc739(0x20c)][_0x4dc739(0x1f2)]&0x1)==0x1&&console[_0x4dc739(0x224)](_0x4dc739(0x1d8));var _0x13edd7=function(_0x2ae17f){const _0x22aa1e=_0x4dc739;(_0x39cf04[_0x22aa1e(0x20c)][_0x22aa1e(0x1f2)]&0x1)==0x1&&console[_0x22aa1e(0x224)](_0x22aa1e(0x213)),(_0x39cf04[_0x22aa1e(0x20c)][_0x22aa1e(0x1f2)]&0x10000)==0x10000&&console[_0x22aa1e(0x224)](_0x22aa1e(0x1f4)),_0x2ad541&&_0x2ad541();},_0x4e5f6d=function(){const _0x22ed95=_0x4dc739;(_0x39cf04[_0x22ed95(0x20c)][_0x22ed95(0x1f2)]&0x10000)==0x10000&&console['log'](_0x22ed95(0x20f)),_0xe7fa03&&_0xe7fa03();};if(_0x39cf04[_0x4dc739(0x20c)]['sdkPlatform']==a0_0x431d17[_0x4dc739(0x1f8)])dd['getBluetoothAdapterState']({'success':_0x13edd7,'fail':_0x4e5f6d});else _0x39cf04[_0x4dc739(0x20c)][_0x4dc739(0x234)]==a0_0x431d17[_0x4dc739(0x240)]?tt['getBluetoothAdapterState']({'success':_0x13edd7,'fail':_0x4e5f6d}):wx[_0x4dc739(0x1d3)]({'success':_0x13edd7,'fail':_0x4e5f6d});},'_openBleAdapter':function(_0x1ed681,_0x5247bb){const _0x151697=a0_0xc21e;let _0x2aaefb=this;var _0x5f20ee=function(){const _0x4bf31a=a0_0xc21e;(_0x2aaefb[_0x4bf31a(0x20c)][_0x4bf31a(0x1f2)]&0x10000)==0x10000&&console[_0x4bf31a(0x224)](_0x4bf31a(0x254)),_0x1ed681&&_0x1ed681();},_0xe6c771=function(){const _0x3ffbb6=a0_0xc21e;(_0x2aaefb['manager']['debug']&0x10000)==0x10000&&console[_0x3ffbb6(0x224)](_0x3ffbb6(0x1d1)),_0x5247bb&&_0x5247bb();};if(_0x2aaefb[_0x151697(0x20c)][_0x151697(0x234)]==a0_0x431d17[_0x151697(0x1f8)])dd[_0x151697(0x239)]({'autoClose':![],'mode':_0x151697(0x210),'success':_0x5f20ee,'fail':_0xe6c771});else _0x2aaefb['manager'][_0x151697(0x234)]==a0_0x431d17[_0x151697(0x240)]?tt['openBluetoothAdapter']({'mode':'central','success':_0x5f20ee,'fail':_0xe6c771}):wx[_0x151697(0x239)]({'mode':_0x151697(0x210),'success':_0x5f20ee,'fail':_0xe6c771});},'_onBleAdapterStateChange':function(){const _0xc80990=a0_0xc21e;let _0x26f3bb=this;if(_0x26f3bb[_0xc80990(0x20c)][_0xc80990(0x234)]==a0_0x431d17[_0xc80990(0x1f8)])dd['onBluetoothAdapterStateChange'](_0x8c1bf1=>{const _0xb80261=_0xc80990;_0x26f3bb['bleAdapterStateReady']=_0x8c1bf1[_0xb80261(0x1fa)],_0x26f3bb[_0xb80261(0x25b)]=_0x8c1bf1['discovering'],_0x8c1bf1['available']==![]&&(_0x26f3bb[_0xb80261(0x20c)]&&_0x26f3bb['manager'][_0xb80261(0x219)]&&(_0x26f3bb['manager']['needCMD']==0xb3||_0x26f3bb[_0xb80261(0x20c)][_0xb80261(0x248)]==0x2||_0x26f3bb['manager'][_0xb80261(0x248)]==0xd3||_0x26f3bb[_0xb80261(0x20c)][_0xb80261(0x248)]==0x14||_0x26f3bb[_0xb80261(0x20c)][_0xb80261(0x248)]==0xe4||_0x26f3bb['manager'][_0xb80261(0x248)]==0xf4||_0x26f3bb[_0xb80261(0x20c)][_0xb80261(0x248)]==0x71)&&_0x26f3bb[_0xb80261(0x20c)][_0xb80261(0x219)][_0xb80261(0x216)](a0_0x431d17[_0xb80261(0x1dd)],_0x26f3bb['manager'][_0xb80261(0x219)][_0xb80261(0x1fc)]),_0x26f3bb['bleDevice']!=null&&_0x26f3bb[_0xb80261(0x218)][_0xb80261(0x1f5)]!=null&&(_0x26f3bb[_0xb80261(0x218)][_0xb80261(0x1f5)]=null,_0x26f3bb[_0xb80261(0x218)]=null,_0x26f3bb[_0xb80261(0x241)]&&_0x26f3bb[_0xb80261(0x241)]())),(_0x26f3bb['manager'][_0xb80261(0x1f2)]&0x1)==0x1&&(console[_0xb80261(0x224)]('适配器状态发送变化:'),console['log'](_0x8c1bf1));});else _0x26f3bb[_0xc80990(0x20c)][_0xc80990(0x234)]==a0_0x431d17[_0xc80990(0x240)]?(tt[_0xc80990(0x202)](),tt['onBluetoothAdapterStateChange'](_0xfae157=>{const _0x1c2848=_0xc80990;_0x26f3bb[_0x1c2848(0x23f)]=_0xfae157[_0x1c2848(0x1fa)],_0x26f3bb[_0x1c2848(0x25b)]=_0xfae157[_0x1c2848(0x211)],_0xfae157['available']==![]&&(tt['offBLEConnectionStateChange'](),tt['offBluetoothDeviceFound'](),tt[_0x1c2848(0x202)](),tt[_0x1c2848(0x1d0)](),_0x26f3bb[_0x1c2848(0x20c)]&&_0x26f3bb[_0x1c2848(0x20c)]['printer']&&(_0x26f3bb[_0x1c2848(0x20c)]['needCMD']==0xb3||_0x26f3bb['manager'][_0x1c2848(0x248)]==0x2||_0x26f3bb[_0x1c2848(0x20c)][_0x1c2848(0x248)]==0xd3||_0x26f3bb[_0x1c2848(0x20c)][_0x1c2848(0x248)]==0x14||_0x26f3bb['manager']['needCMD']==0xe4||_0x26f3bb[_0x1c2848(0x20c)][_0x1c2848(0x248)]==0xf4||_0x26f3bb[_0x1c2848(0x20c)][_0x1c2848(0x248)]==0x71)&&_0x26f3bb['manager'][_0x1c2848(0x219)][_0x1c2848(0x216)](a0_0x431d17['JCSDK_PRINT_ERR_DISCONNECT'],_0x26f3bb[_0x1c2848(0x20c)][_0x1c2848(0x219)][_0x1c2848(0x1fc)]),_0x26f3bb['bleDevice']!=null&&_0x26f3bb[_0x1c2848(0x218)][_0x1c2848(0x1f5)]!=null&&(_0x26f3bb[_0x1c2848(0x218)][_0x1c2848(0x1f5)]=null,_0x26f3bb[_0x1c2848(0x218)]=null,_0x26f3bb['openPrinterCallbackFaill']&&_0x26f3bb['openPrinterCallbackFaill']())),(_0x26f3bb[_0x1c2848(0x20c)][_0x1c2848(0x1f2)]&0x1)==0x1&&(console[_0x1c2848(0x224)](_0x1c2848(0x1de)),console[_0x1c2848(0x224)](_0xfae157));})):(wx[_0xc80990(0x202)](),wx[_0xc80990(0x25a)](_0x291bff=>{const _0x5d594f=_0xc80990;_0x26f3bb[_0x5d594f(0x23f)]=_0x291bff['available'],_0x26f3bb[_0x5d594f(0x25b)]=_0x291bff[_0x5d594f(0x211)],_0x291bff['available']==![]&&(wx['offBLEConnectionStateChange'](),wx[_0x5d594f(0x1fd)](),wx['offBluetoothAdapterStateChange'](),wx['closeBluetoothAdapter'](),_0x26f3bb[_0x5d594f(0x20c)]&&_0x26f3bb[_0x5d594f(0x20c)][_0x5d594f(0x219)]&&(_0x26f3bb[_0x5d594f(0x20c)][_0x5d594f(0x248)]==0xb3||_0x26f3bb['manager'][_0x5d594f(0x248)]==0x2||_0x26f3bb['manager'][_0x5d594f(0x248)]==0xd3||_0x26f3bb['manager'][_0x5d594f(0x248)]==0x14||_0x26f3bb['manager'][_0x5d594f(0x248)]==0xe4||_0x26f3bb[_0x5d594f(0x20c)][_0x5d594f(0x248)]==0xf4||_0x26f3bb[_0x5d594f(0x20c)]['needCMD']==0x71)&&_0x26f3bb['manager'][_0x5d594f(0x219)][_0x5d594f(0x216)](a0_0x431d17['JCSDK_PRINT_ERR_DISCONNECT'],_0x26f3bb[_0x5d594f(0x20c)][_0x5d594f(0x219)][_0x5d594f(0x1fc)]),_0x26f3bb[_0x5d594f(0x218)]!=null&&_0x26f3bb[_0x5d594f(0x218)]['deviceId']!=null&&(_0x26f3bb['bleDevice']['deviceId']=null,_0x26f3bb[_0x5d594f(0x218)]=null,_0x26f3bb[_0x5d594f(0x241)]&&_0x26f3bb[_0x5d594f(0x241)]())),(_0x26f3bb['manager'][_0x5d594f(0x1f2)]&0x1)==0x1&&(console[_0x5d594f(0x224)](_0x5d594f(0x1de)),console[_0x5d594f(0x224)](_0x291bff));}));},'reciveCmd':function(_0x70dc29,_0x4e2abc){const _0x331fbc=a0_0xc21e;let _0x4317aa=this;switch(_0x70dc29){case 0xc2:{function _0xb2e15e(){const _0x4eb4b6=a0_0xc21e;if(_0x4317aa[_0x4eb4b6(0x20c)][_0x4eb4b6(0x234)]==a0_0x431d17[_0x4eb4b6(0x1f8)])dd['disconnectBLEDevice']({'deviceId':this['bleDevice'][_0x4eb4b6(0x1f5)],'success':()=>{},'fail':()=>{},'complete':()=>{}});else _0x4317aa['manager']['sdkPlatform']==a0_0x431d17[_0x4eb4b6(0x240)]?tt['disconnectBLEDevice']({'deviceId':this[_0x4eb4b6(0x218)][_0x4eb4b6(0x1f5)]}):wx[_0x4eb4b6(0x221)]({'deviceId':this[_0x4eb4b6(0x218)][_0x4eb4b6(0x1f5)]});}if(this['manager'][_0x331fbc(0x248)]==0xc2){if(_0x4e2abc==0x2||_0x4e2abc==0x1||_0x4e2abc==0x3){this[_0x331fbc(0x23d)]&&clearTimeout(this[_0x331fbc(0x23d)]);if(_0x4e2abc==0x1){if(this['openPrinterCallbackFaill']){let _0x125a3e=this;var _0x274825=this[_0x331fbc(0x241)];this[_0x331fbc(0x241)]=function(){const _0x3acef4=_0x331fbc;_0x125a3e[_0x3acef4(0x218)]=null,_0x125a3e[_0x3acef4(0x20c)]['printer']=null,_0x274825();};}_0xb2e15e();return;}this['manager'][_0x331fbc(0x1ed)]=_0x4e2abc;let _0x2d3c77=this;this[_0x331fbc(0x20c)][_0x331fbc(0x232)]=function(){const _0x6e421=_0x331fbc;_0x2d3c77[_0x6e421(0x23d)]>0x0&&(clearTimeout(_0x2d3c77['repeatTimeoutId']),_0x2d3c77[_0x6e421(0x23d)]=null);let _0xfacb19=a0_0x3c2c77[_0x6e421(0x24e)](0x40,0x8);_0x2d3c77[_0x6e421(0x20c)]['needCMD']=0x48,_0x2d3c77[_0x6e421(0x231)](_0xfacb19,0x9,()=>{const _0x2b0edf=_0x6e421;if(_0x2d3c77[_0x2b0edf(0x241)]){var _0x131801=_0x2d3c77[_0x2b0edf(0x241)];_0x2d3c77[_0x2b0edf(0x241)]=function(){const _0x4c610b=_0x2b0edf;_0x2d3c77[_0x4c610b(0x218)]=null,_0x2d3c77['manager'][_0x4c610b(0x219)]=null,_0x131801();};}_0xb2e15e();});},this['manager'][_0x331fbc(0x248)]=0xb5;let _0x500c9e=a0_0x3c2c77[_0x331fbc(0x24e)](0xa5,0x1);this[_0x331fbc(0x231)](_0x500c9e,0x9,()=>{const _0x36ed0c=_0x331fbc;if(_0x2d3c77[_0x36ed0c(0x241)]){var _0x2305fa=_0x2d3c77[_0x36ed0c(0x241)];_0x2d3c77[_0x36ed0c(0x241)]=function(){const _0x23e0df=_0x36ed0c;_0x2d3c77[_0x23e0df(0x218)]=null,_0x2d3c77['manager'][_0x23e0df(0x219)]=null,_0x2305fa();};}_0xb2e15e();});}}}break;case 0x48:{this[_0x331fbc(0x20c)]['needCMD']==0x48&&(this[_0x331fbc(0x23d)]&&clearTimeout(this[_0x331fbc(0x23d)]),this[_0x331fbc(0x20c)][_0x331fbc(0x1d5)](_0x4e2abc));}break;default:break;}},'openStatus':function(_0x1e4ec7){const _0x3942e7=a0_0xc21e;let _0x4f954c=this;if(_0x1e4ec7)this[_0x3942e7(0x227)]&&this[_0x3942e7(0x227)]();else{if(this['bleDevice']&&this[_0x3942e7(0x218)][_0x3942e7(0x1f5)]){if(_0x4f954c[_0x3942e7(0x20c)][_0x3942e7(0x234)]==a0_0x431d17[_0x3942e7(0x1f8)])dd[_0x3942e7(0x1d4)]({'deviceId':this[_0x3942e7(0x218)][_0x3942e7(0x1f5)],'success':()=>{},'fail':()=>{},'complete':()=>{}});else _0x4f954c[_0x3942e7(0x20c)][_0x3942e7(0x234)]==a0_0x431d17[_0x3942e7(0x240)]?tt[_0x3942e7(0x1d4)]({'deviceId':this[_0x3942e7(0x218)][_0x3942e7(0x1f5)],'succ':_0xa3c35f=>{},'fail':_0x4edb64=>{},'complete':()=>{}}):wx[_0x3942e7(0x221)]({'deviceId':this[_0x3942e7(0x218)][_0x3942e7(0x1f5)]});}if(this['openPrinterCallbackFaill']){let _0x36a938=this[_0x3942e7(0x241)];this['openPrinterCallbackFaill']=null,_0x36a938();}}}};export default Ble;