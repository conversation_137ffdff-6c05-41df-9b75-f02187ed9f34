import JCAPI from "../../utils/JCAPI/JCAPI";
// index.js
// 获取应用实例
const app = getApp()
let jishu = 0;
Page({
    data: {
        canvasWidth: 400,
        canvasHeight: 240,
        // 如需尝试获取用户信息可改为false
        pageModels: [],
        showPrinters: false,
        printerLists: [],
        nowPrint: null,
        showPrinting: false,
        printedCount: 0,
        showOver: 0,//0:刚启动 1:点了取消 2:取消成功 3:正常打印完成 4:打印出错停止
        printingError: null,
        switch2Checked: false,
        // 预览功能相关数据
        showPreview: false,
        previewImagePath: '',
        currentPreviewIndex: 0,
        previewImages: [] // 存储所有页面的预览图片
    },

    getSN() {
        let deviceInfo = JCAPI.getConnName()
        if(deviceInfo!==null){
          console.log("deviceInfo", JCAPI.getConnName())
          console.log("deviceName", JCAPI.getConnName().name)
        }
        JCAPI.getSN(function (res) {
            console.log(res)
            let des = '';
            if (res.code === 0) {
                des = res.res;
            } else if (res.code === -1) {
                des = '获取失败';
            } else if (res.code === -2) {
                des = '打印机忙碌';
            } else if (res.code === -3) {
                des = '不支持';
            } else if (res.code === -4) {
                des = '打印机未连接/断开/超时';
            }
            wx.showToast({title: des})
        });

    },
    getSoftVersion() {
        JCAPI.getSoftVersion(function (res) {
            console.log(res)
            let des = '';
            if (res.code === 0) {
                des = res.res;
            } else if (res.code === -1) {
                des = '获取失败';
            } else if (res.code === -2) {
                des = '打印机忙碌';
            } else if (res.code === -3) {
                des = '不支持';
            } else if (res.code === -4) {
                des = '打印机未连接/断开/超时';
            }
            wx.showToast({title: des})
        });

    },
    getHardVersion() {
        JCAPI.getHardVersion(function (res) {
            console.log(res)
            let des = '';
            if (res.code === 0) {
                des = res.res;
            } else if (res.code === -1) {
                des = '获取失败';
            } else if (res.code === -2) {
                des = '打印机忙碌';
            } else if (res.code === -3) {
                des = '不支持';
            } else if (res.code === -4) {
                des = '打印机未连接/断开/超时';
            }
            wx.showToast({title: des})
        });

    },
    getMultiple() {
        wx.showToast({title: JCAPI.getMultiple() + ""})

    },
    getSpeed() {
        JCAPI.gePrintSpeedQualityWay(function (res) {
            console.log(res)
            var des = '';
            if (res.code === 0) {
                des = res.res;
            } else if (res.code === -1) {
                des = '获取失败';
            } else if (res.code === -2) {
                des = '打印机忙碌';
            } else if (res.code === -3) {
                des = '不支持';
            } else if (res.code === -4) {
                des = '打印机未连接/断开/超时';
            }
            wx.showToast({title: '' + des})
        });
    },
    openPrinter(e) {
        let item = {}
        if (wx.getStorageSync('print')) {
            item = wx.getStorageSync('print')
        }
        if (e && e.currentTarget.dataset.item) {
            item = e.currentTarget.dataset.item
        }
        let self = this;
        // let name = "S6-D726050099"
        wx.showLoading({
            title: '连接中',
            mask: true
        });
        JCAPI.openPrinter(item.name, function () {
            wx.showToast({
                title: '连接打印机成功',
                icon: '',
                image: '',
                duration: 2000,
                mask: true,
                success: function (res) {
                    wx.hideLoading();
                    let Printer = self.data.printerLists;
                    var arr = Printer.filter(function (items) {
                        return items.name !== item.name;
                    });
                    wx.setStorageSync('print', item)
                    self.setData({
                        nowPrint: item,
                        printerLists: arr
                    })
                },
            })
        }, function () {
            self.setData({
                nowPrint: ""
            })
            wx.removeStorageSync("print")
            wx.showToast({
                title: '连接失败',
                icon: 'none',
                duration: 2000
            })
        });
    },

    scanPrinter() {
        let self = this;
        // if(wx.getStorageSync('print')){
        //   this.setData({
        //     nowPrint: wx.getStorageSync('print')
        //   })
        // }
        wx.showLoading({
            title: '搜索中',
            mask: true
        })
        JCAPI.scanedPrinters((didGetScanedPrinters) => {
            wx.hideLoading()
            let arr = didGetScanedPrinters
            arr = didGetScanedPrinters.filter(items => {
                return items.name.indexOf('未知') < 0;
                return items;
            })
            if (self.data.nowPrint && self.data.nowPrint.name) {
                arr = arr.filter(items => {
                    return items.name !== self.data.nowPrint.name
                });
            }
            this.setData({
                showPrinters: true,
                printerLists: arr
            })
        })
    },

    disconnect: function (e) {
        let self = this
        // wx.hideLoading()
        wx.showToast({
            title: '打印机连接断开',
            icon: '',
            image: '',
            duration: 2000,
            mask: true,
            success: function (res) {
                self.setData({
                    nowPrint: ""
                })
                wx.removeStorageSync("print")
                // self.scanedPrinters()
            },
            fail: function (res) {
            },
            complete: function (res) {
            },
        })
        JCAPI.closePrinter();
    },

    overPrint(e) {
        console.log(e);
        let self = this;
        let status = e.currentTarget.dataset.status;
        if (status < 2) {
            this.data.showOver = 1;
            JCAPI.cancelPrint(() => {
                self.setData({
                    showOver: 2
                })
            });
        } else {
            this.setData({
                showOver: 0,
                printingError: null,
                showPrinting: false
            })
        }
    },

    cancelprint(e) {
        JCAPI.cancelPrint(() => {
            let ctx = wx.createCanvasContext('test1', this);
            ctx.setFillStyle('white');
            ctx.fillRect(0, 0, 200, 80);
            ctx.draw();
            ctx.setFillStyle('black');
            ctx.setFontSize(10);
            ctx.fillText('已取消成功', 3, 30);
            ctx.draw();
        });
    },


    scanBarcode(e) {
        JCAPI.setUseThirdBleListen(true);
        wx.onBLECharacteristicValueChange((res) => {
            console.log("-----外置蓝牙监听数据")
            JCAPI.bleValueChanged(res);
        });
        return;
        wx.scanCode({
            onlyFromCamera: true,
            scanType: 'barCode',
            success: (res) => {
                console.log(res);
                let ctx = wx.createCanvasContext('test1', this);
                ctx.setFillStyle('white');
                ctx.fillRect(0, 0, 200, 80);
                ctx.draw();
                ctx.setFillStyle('black');
                ctx.setFontSize(10);
                ctx.fillText(res.result, 3, 30);
                ctx.draw();
            }
        })
    },

    print(e) {
        if (this.data.pageModels.length == 0) {
            wx.showToast({
                title: '请添加打印内容',
                icon: 'none',
                duration: 2000
            })
            return
        }
        if (!this.data.nowPrint || !this.data.nowPrint.name) {
            this.scanPrinter()
            return
        }
        wx.setStorageSync('lastPrintData', this.data.pageModels);
        let count = 0;
        let gapType = 0;
        let darkness = 0;
        let self = this;
        for (let index = 0; index < this.data.pageModels.length; index++) {
            let item = this.data.pageModels[index];
            count += item.count;
            if (index == 0) {
                gapType = item.gapType;
                darkness = item.darkness;
            }
        }
        // this.setData({
        //   showPrinting:true,
        //   printingError:null,
        //   printedCount:0
        // })
        let ctx = wx.createCanvasContext('test1', this);
        ctx.setFillStyle('white');
        ctx.fillRect(0, 0, 200, 80);
        ctx.draw();
        ctx.setFillStyle('black');
        ctx.setFontSize(10);
        ctx.fillText('准备打印', 3, 30);
        ctx.draw();
        JCAPI.didReadPrintCountInfo(function (res) {
            console.log("-----收到页面变化数据:----");
            console.log(res);
            if (count == res.count) {
                // self.setData({
                //   printingError:null,
                //   showOver:3,
                //   printedCount:res
                // })
                ctx.setFillStyle('black');
                ctx.setFontSize(10);
                ctx.fillText('打印完:' + res.count + '页', 3, 30);
                if (res.tid) {
                    ctx.fillText('tid=' + res.tid, 50, 30);
                }
                ctx.draw();
            } else {
                // self.setData({
                //   printedCount:res
                // })
                ctx.setFillStyle('black');
                ctx.setFontSize(10);
                ctx.fillText('打印了:' + res.count + '页', 3, 30);
                if (res.tid) {
                    ctx.fillText('tid=' + res.tid, 50, 30);
                }
                ctx.draw();
            }
        });
        JCAPI.didReadPrintErrorInfo(function (res) {
            console.log("-----收到出错信息 :----");
            console.log(res);
            // self.setData({
            //   printingError:res,
            //   showOver:4
            // })
            ctx.setFillStyle('black');
            ctx.setFontSize(10);
            ctx.fillText('错误:' + res.errCode + ' ' + res.msg, 3, 30);
            ctx.draw();
        });
        jishu = 0;
        JCAPI.startJob(gapType, darkness, count, function () {
            self.startPrinting();
        });
    },

    startPrinting: function () {
        if (this.data.pageModels.length <= jishu) return;
        let self = this;
        let item = this.data.pageModels[jishu];
        let pageCount = item.count;
        JCAPI.startDrawLabel('test', this, item.w, item.h, item.rotation);
        let elements = item.elements;
        for (let i = 0; i < elements.length; i++) {
            let element = elements[i];
            if (element.id == 1) {
                JCAPI.drawText(element.value, element.x, element.y, element.fontSize, element.rotation, element.options);
            } else if (element.id == 2) {
                JCAPI.drawLine(element.x, element.y, element.w, element.h, element.rotation);
            } else if (element.id == 3) {
                JCAPI.drawBarcode(element.value, element.x, element.y, element.w, element.h, element.rotation, element.fontSize, element.fontHeight, element.fontPostion);
            } else if (element.id == 4) {
                JCAPI.drawQRCode(element.value, element.x, element.y, element.w, element.h, element.rotation);
            } else if (element.id == 5) {
                JCAPI.drawRectangle(element.x, element.y, element.w, element.h, element.linewidth, element.filled, element.rotation);
            } else if (element.id == 6) {
                JCAPI.drawImage(element.value, element.x, element.y, element.w, element.h, element.rotation);
            }
        }
        var options = {}
        if (item.epc) {
            options['epc'] = item.epc;
        }
        if (item.speedChecked) {
            options['speedOrQuality'] = 1;
        }
        console.log(options)
        console.log(item)
        JCAPI.endDrawLabel(function () {
            JCAPI.print(pageCount, function () {
                jishu++;
                self.startPrinting();
            }, options);
        });
    },

    // 预览单个页面
    drawPerview: function (pageIndex = 0) {
        if (this.data.pageModels.length === 0) {
            wx.showToast({
                title: '请先添加打印内容',
                icon: 'none',
                duration: 2000
            });
            return;
        }

        if (pageIndex >= this.data.pageModels.length) {
            pageIndex = 0;
        }

        // 显示加载提示
        wx.showLoading({
            title: '生成预览中...',
            mask: true
        });

        let self = this;
        let item = this.data.pageModels[pageIndex];

        // 开始绘制标签
        JCAPI.startDrawLabel('test', this, item.w, item.h, item.rotation);
        let elements = item.elements || [];

        // 绘制所有元素
        for (let i = 0; i < elements.length; i++) {
            let element = elements[i];
            if (element.id == 1) {
                JCAPI.drawText(element.value, element.x, element.y, element.fontSize, element.rotation, element.options);
            } else if (element.id == 2) {
                JCAPI.drawLine(element.x, element.y, element.w, element.h, element.rotation);
            } else if (element.id == 3) {
                JCAPI.drawBarcode(element.value, element.x, element.y, element.w, element.h, element.rotation, element.fontSize, element.fontHeight, element.fontPostion);
            } else if (element.id == 4) {
                JCAPI.drawQRCode(element.value, element.x, element.y, element.w, element.h, element.rotation);
            } else if (element.id == 5) {
                JCAPI.drawRectangle(element.x, element.y, element.w, element.h, element.linewidth, element.filled, element.rotation);
            } else if (element.id == 6) {
                JCAPI.drawImage(element.value, element.x, element.y, element.w, element.h, element.rotation);
            }
        }

        // 结束绘制并生成预览图片
        JCAPI.endDrawLabel(function () {
            wx.canvasToTempFilePath({
                x: 0,
                y: 0,
                width: item.w * 8,
                height: item.h * 8,
                canvasId: 'test',
                success: (res) => {
                    // 隐藏加载提示
                    wx.hideLoading();

                    // 更新预览图片路径和显示预览
                    self.setData({
                        previewImagePath: res.tempFilePath,
                        currentPreviewIndex: pageIndex,
                        showPreview: true
                    });

                    // 保存到预览图片数组
                    let previewImages = self.data.previewImages;
                    previewImages[pageIndex] = res.tempFilePath;
                    self.setData({
                        previewImages: previewImages
                    });
                },
                fail: (error) => {
                    // 隐藏加载提示
                    wx.hideLoading();
                    console.error('生成预览图片失败:', error);
                    wx.showToast({
                        title: '预览生成失败',
                        icon: 'none',
                        duration: 2000
                    });
                }
            }, self);
        });
    },

    // 预览所有页面
    previewAllPages: function () {
        if (this.data.pageModels.length === 0) {
            wx.showToast({
                title: '请先添加打印内容',
                icon: 'none',
                duration: 2000
            });
            return;
        }

        // 从第一页开始预览
        this.drawPerview(0);
    },

    // 切换到上一页预览
    prevPreview: function () {
        let currentIndex = this.data.currentPreviewIndex;
        let newIndex = currentIndex > 0 ? currentIndex - 1 : this.data.pageModels.length - 1;
        this.drawPerview(newIndex);
    },

    // 切换到下一页预览
    nextPreview: function () {
        let currentIndex = this.data.currentPreviewIndex;
        let newIndex = currentIndex < this.data.pageModels.length - 1 ? currentIndex + 1 : 0;
        this.drawPerview(newIndex);
    },

    // 关闭预览
    closePreview: function () {
        this.setData({
            showPreview: false,
            previewImagePath: ''
        });
    },

    // 保存预览图片到相册
    savePreviewImage: function () {
        if (!this.data.previewImagePath) {
            wx.showToast({
                title: '没有可保存的图片',
                icon: 'none',
                duration: 2000
            });
            return;
        }

        wx.saveImageToPhotosAlbum({
            filePath: this.data.previewImagePath,
            success: () => {
                wx.showToast({
                    title: '保存成功',
                    icon: 'success',
                    duration: 2000
                });
            },
            fail: (err) => {
                if (err.errMsg.indexOf('auth') !== -1) {
                    wx.showModal({
                        title: '提示',
                        content: '需要您授权保存图片到相册',
                        success: (res) => {
                            if (res.confirm) {
                                wx.openSetting();
                            }
                        }
                    });
                } else {
                    wx.showToast({
                        title: '保存失败',
                        icon: 'none',
                        duration: 2000
                    });
                }
            }
        });
    },

    // 预览指定页面
    previewPage: function (e) {
        let pageIndex = e.currentTarget.dataset.index;
        let pageModel = this.data.pageModels[pageIndex];

        // 检查页面是否有元素
        if (!pageModel.elements || pageModel.elements.length === 0) {
            wx.showModal({
                title: '提示',
                content: '当前页面没有任何元素，是否继续预览空白页面？',
                success: (res) => {
                    if (res.confirm) {
                        this.drawPerview(pageIndex);
                    }
                }
            });
        } else {
            this.drawPerview(pageIndex);
        }
    },

    addPage: function () {
        let datas = this.data.pageModels;
        let model = {w: 50, h: 30, rotation: 0, count: 1, gapType: 1, darkness: 3, elements: []};
        if (datas.length == 0) {
            model.speedChecked = false;//高速，高质量
        }
        datas.push(model);
        this.setData({
            pageModels: datas
        })
    },

    switch2Change: function (e) {
        let datas = this.data.pageModels;
        let index = e.currentTarget.dataset.item;
        let item = datas[index];
        if (index == 0) {
            if (item.speedChecked) {
                item.speedChecked = false;
            } else {
                item.speedChecked = true;
            }
            this.setData({
                pageModels: datas
            })
        }
    },

    addText: function (e) {
        let datas = this.data.pageModels;
        let index = e.currentTarget.dataset.item;
        let item = datas[index];
        let model = {id: 1, x: 18, y: 6, fontSize: 2.2, rotation: 0, value: '流水号:' + datas.length, options: null};
        item.elements.push(model);
        this.setData({
            pageModels: datas
        })
    },

    addLine: function (e) {
        let datas = this.data.pageModels;
        let index = e.currentTarget.dataset.item;
        let item = datas[index];
        let model = {id: 2, x: 3, y: 7, w: 5, h: 1, rotation: 0};
        item.elements.push(model);
        this.setData({
            pageModels: datas
        })
    },

    addBarcode: function (e) {
        let datas = this.data.pageModels;
        let index = e.currentTarget.dataset.item;
        let item = datas[index];
        let model = {
            id: 3,
            x: 3,
            y: 7,
            w: 16,
            h: 7,
            rotation: 0,
            value: '12345678',
            fontSize: 2.5,
            fontHeight: 3,
            fontPostion: 2
        };
        item.elements.push(model);
        this.setData({
            pageModels: datas
        })
    },

    addQrcode: function (e) {
        let datas = this.data.pageModels;
        let index = e.currentTarget.dataset.item;
        let item = datas[index];
        let model = {id: 4, x: 3, y: 3, w: 12, h: 12, rotation: 0, value: '12345678'};
        item.elements.push(model);
        this.setData({
            pageModels: datas
        })
    },

    addRect: function (e) {
        let datas = this.data.pageModels;
        let index = e.currentTarget.dataset.item;
        let item = datas[index];
        let model = {id: 5, x: 3, y: 7, w: 10, h: 8, rotation: 0, linewidth: 1, filled: false};
        item.elements.push(model);
        this.setData({
            pageModels: datas
        })
    },

    addPicture: function (e) {
        let datas = this.data.pageModels;
        let index = e.currentTarget.dataset.item;
        let item = datas[index];
        let model = {id: 6, x: 0, y: 0, w: 20, h: 20, rotation: 0};
        item.elements.push(model);
        this.setData({
            pageModels: datas
        })
    },

    delElement: function (e) {
        let datas = this.data.pageModels;
        let index = e.currentTarget.dataset.item;
        let parentIndex = e.currentTarget.dataset.parent;
        let item = datas[parentIndex];
        item.elements.splice(index, 1);
        this.setData({
            pageModels: datas
        })
    },

    delPage: function (e) {
        let datas = this.data.pageModels;
        let index = e.currentTarget.dataset.item;
        datas.splice(index, 1);
        this.setData({
            pageModels: datas
        })
    },

    onLoad() {
        if (wx.getUserProfile) {
            this.setData({
                canIUseGetUserProfile: true
            })
        }
        let item = []
        if (wx.getStorageSync('lastPrintData')) {
            item = wx.getStorageSync('lastPrintData')
        }
        this.setData({
            pageModels: item
        })
    },
    onShow() {
        if (app.currentData) {
            let models = app.currentData;
            app.currentData = null;
            this.setData({
                pageModels: models
            })
        }
    },
    getUserProfile(e) {
        // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
        wx.getUserProfile({
            desc: '展示用户信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
            success: (res) => {
                console.log(res)
                this.setData({
                    userInfo: res.userInfo,
                    hasUserInfo: true
                })
            }
        })
    },
    getUserInfo(e) {
        // 不推荐使用getUserInfo获取用户信息，预计自2021年4月13日起，getUserInfo将不再弹出弹窗，并直接返回匿名的用户个人信息
        console.log(e)
        this.setData({
            userInfo: e.detail.userInfo,
            hasUserInfo: true
        })
    },

    inputGapType(e) {
        let item = e.currentTarget.dataset.item;
        this.data.pageModels[item].gapType = parseInt(e.detail.value);
    },

    inputDarkness(e) {
        let item = e.currentTarget.dataset.item;
        this.data.pageModels[item].darkness = parseInt(e.detail.value);
    },

    inputPageW(e) {
        let item = e.currentTarget.dataset.item;
        this.data.pageModels[item].w = parseFloat(e.detail.value);
    },

    inputPageH(e) {
        let item = e.currentTarget.dataset.item;
        this.data.pageModels[item].h = parseFloat(e.detail.value);
    },

    inputPageRotation(e) {
        let item = e.currentTarget.dataset.item;
        this.data.pageModels[item].rotation = parseInt(e.detail.value);
    },

    inputEpc(e) {
        let item = e.currentTarget.dataset.item;
        this.data.pageModels[item].epc = e.detail.value;
    },

    inputPageNumber(e) {
        let item = e.currentTarget.dataset.item;
        let count = parseInt(e.detail.value);
        this.data.pageModels[item].count = count;
    },


    inputElementX(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        this.data.pageModels[parentIndex].elements[childIndex].x = parseFloat(e.detail.value);
        console.log(this.data.pageModels);
    },

    inputElementY(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        this.data.pageModels[parentIndex].elements[childIndex].y = parseFloat(e.detail.value);
        console.log(this.data.pageModels);
    },

    inputElementW(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        this.data.pageModels[parentIndex].elements[childIndex].w = parseFloat(e.detail.value);
        console.log(this.data.pageModels);
    },

    inputElementH(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        this.data.pageModels[parentIndex].elements[childIndex].h = parseFloat(e.detail.value);
        console.log(this.data.pageModels);
    },

    inputElementFontHeight(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        this.data.pageModels[parentIndex].elements[childIndex].fontSize = parseFloat(e.detail.value);
        console.log(this.data.pageModels);
    },

    inputElementBarcodeFontHeight(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        this.data.pageModels[parentIndex].elements[childIndex].fontHeight = parseFloat(e.detail.value);
        console.log(this.data.pageModels);
    },

    inputElementFontPostion(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        let value = parseInt(e.detail.value);
        console.log(value);
        if (value != 0 && value != 1 && value != 2) return;
        this.data.pageModels[parentIndex].elements[childIndex].fontPostion = parseInt(e.detail.value);
        console.log(this.data.pageModels);
    },

    inputElementValue(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        this.data.pageModels[parentIndex].elements[childIndex].value = e.detail.value;
        console.log(this.data.pageModels);
    },

    inputElementLineWidth(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        this.data.pageModels[parentIndex].elements[childIndex].linewidth = parseFloat(e.detail.value);
        console.log(this.data.pageModels);
    },

    inputElementRotation(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        this.data.pageModels[parentIndex].elements[childIndex].rotation = parseInt(e.detail.value);
        console.log(this.data.pageModels);
    },

    filledChanged(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        this.data.pageModels[parentIndex].elements[childIndex].filled = e.detail.value;
        console.log(this.data.pageModels);
    },

    selectPic(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        // this.data.pageModels[parentIndex].elements[childIndex].value = e.detail.value;
        // console.log(this.data.pageModels);
        let self = this;
        wx.showActionSheet({
            itemList: ['从手机相册选择'],
            success: (res) => {
                if (res.tapIndex == 0) {
                    wx.chooseImage({
                        count: 1, // 默认9
                        sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
                        sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
                        success: function (res) {
                            // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
                            var tempFilePaths = res.tempFilePaths;
                            if (tempFilePaths.length > 0) {
                                self.data.pageModels[parentIndex].elements[childIndex].value = tempFilePaths[0];
                                self.setData({
                                    pageModels: self.data.pageModels
                                })
                            }
                        }
                    })
                } else if (res.tapIndex == 1) {
                    const ctx = wx.createCameraContext();
                    ctx.takePhoto({
                        quality: 'high',
                        success: (res) => {
                            self.data.pageModels[parentIndex].elements[childIndex].value = res.tempImagePath;
                            self.setData({
                                pageModels: self.data.pageModels
                            })
                        },
                        fail: (err) => {
                            console.log(err);
                        }
                    })
                }
            },
            fail: (err) => {
                console.log(err);
            }
        })
    },

    switchChange(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        let options = this.data.pageModels[parentIndex].elements[childIndex].options;
        if (options) {
            options.bold = e.detail.value;
        } else {
            options = {};
            options.bold = e.detail.value;
        }
        this.data.pageModels[parentIndex].elements[childIndex].options = options;
        console.log(this.data.pageModels);
    },

    switchChange2(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        let options = this.data.pageModels[parentIndex].elements[childIndex].options;
        if (options) {
            options.italic = e.detail.value;
        } else {
            options = {};
            options.italic = e.detail.value;
        }
        this.data.pageModels[parentIndex].elements[childIndex].options = options;
        console.log(this.data.pageModels);
    },

    inputFontFamily(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        let options = this.data.pageModels[parentIndex].elements[childIndex].options;
        if (options) {
            options.family = e.detail.value;
        } else {
            options = {};
            options.family = e.detail.value;
        }
        this.data.pageModels[parentIndex].elements[childIndex].options = options;
        console.log(this.data.pageModels);
    },

    inputAlign(e) {
        console.log(e);
        let parentIndex = e.currentTarget.dataset.parent;
        let childIndex = e.currentTarget.dataset.item;
        let options = this.data.pageModels[parentIndex].elements[childIndex].options;
        if (options) {
            options.align = e.detail.value;
        } else {
            options = {};
            options.align = e.detail.value;
        }
        this.data.pageModels[parentIndex].elements[childIndex].options = options;
        console.log(this.data.pageModels);
    },

    closeDialog(e) {
        this.setData({
            showPrinters: false
        })
    },

    testDraw() {
        manager.startDrawLabel('test', this, 45, 400, 0);
        manager.drawBarcode("12345678", 18, 6, 18, 20, 0);
        manager.drawRectangle(3, 40, 30, 50, 1, false, 0);
        manager.drawQRCode("12345678", 5, 200, 40, 40, 0);
        manager.drawText("精臣小程序!!!", 3, 48, 2.2, 0);
        manager.drawLine(3, 35, 70, 3, 0);
        manager.drawLine(3, 7, 5, 1, 0);
        manager.drawBarcode("12345678", 3, 350, 30, 50, 0);
        manager.endDrawLabel(function () {
            wx.canvasGetImageData({
                canvasId: 'test',
                height: 3200,
                width: 360,
                x: 0,
                y: 0,
                success: (res) => {
                    function ab2hex(buffer) {
                        const hexArr = Array.prototype.map.call(
                            new Uint8Array(buffer),
                            function (bit) {
                                return ('00' + bit.toString(16)).slice(-2)
                            }
                        )
                        return hexArr.join('')
                    }

                    var datas = DataUntils.dealImageData(res, 200, {left: 0, rifht: 0}, 200, 0);
                    for (var i = 0; i < datas.length; i++) {
                        var arr = datas[i];
                        for (var j = 0; j < arr.length; j++) {
                            var buffer = arr[j];
                            console.log(ab2hex(buffer));
                        }
                    }
                },
                fail: (error) => {
                    // self.printer.sendPrintError(ErrorCode.JCSDK_PRINT_ERR_GetDATA,obj.fail,obj.complete);
                }
            }, this);
        });
    },

    /**
     * 打开精臣B1打印机Demo页面
     * 跳转到printer页面进行B1打印机功能演示
     */
    openB1PrinterDemo() {
        console.log('打开精臣B1打印机Demo页面')

        wx.showModal({
            title: '精臣B1打印机Demo',
            content: '即将进入精臣B1打印机功能演示页面，支持标签打印、预览等功能。\n\n注意：只能连接序列号为 H613070302 的设备。',
            confirmText: '进入Demo',
            cancelText: '取消',
            success: (res) => {
                if (res.confirm) {
                    // 跳转到printer页面
                    wx.navigateTo({
                        url: '/pages/printer/printer',
                        success: () => {
                            console.log('成功跳转到精臣B1打印机Demo页面')
                        },
                        fail: (error) => {
                            console.error('跳转失败:', error)
                            wx.showToast({
                                title: '页面跳转失败',
                                icon: 'none'
                            })
                        }
                    })
                }
            }
        })
    }

})
