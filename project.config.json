{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": true, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": true, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": false, "useApiHostProcess": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true, "useStaticServer": true, "ignoreDevUnusedFiles": false, "condition": false, "compileWorklet": false, "localPlugins": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "2.18.1", "appid": "wxe8d54c4a8631ad57", "projectname": "WXSDK", "simulatorType": "wechat", "simulatorPluginLibVersion": {"qywx_simulator_plugin": "2.4.0"}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}