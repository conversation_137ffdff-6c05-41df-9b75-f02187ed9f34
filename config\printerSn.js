/**
 * 精臣B1打印机序列号配置文件
 * 只允许连接配置列表中的设备
 */

const allowedPrinterSns = [
  {
    sn: 'B1-H613070302',
    name: 'B1-H613070302',
    model: 'B1',
    description: '精臣B1标签打印机，200dpi精度'
  }
  // 可以在这里添加更多允许的设备
];

/**
 * 检查设备序列号是否在允许列表中
 * @param {string} sn 设备序列号
 * @returns {boolean} 是否允许连接
 */
function isAllowedPrinter(sn) {
  return allowedPrinterSns.some(printer => printer.sn === sn);
}

/**
 * 根据序列号获取设备信息
 * @param {string} sn 设备序列号
 * @returns {object|null} 设备信息对象，如果不存在返回null
 */
function getPrinterInfo(sn) {
  return allowedPrinterSns.find(printer => printer.sn === sn) || null;
}

/**
 * 获取所有允许的打印机列表
 * @returns {Array} 允许的打印机列表
 */
function getAllowedPrinters() {
  return JSON.parse(JSON.stringify(allowedPrinterSns));
}

module.exports = {
  allowedPrinterSns,
  isAllowedPrinter,
  getPrinterInfo,
  getAllowedPrinters
};
