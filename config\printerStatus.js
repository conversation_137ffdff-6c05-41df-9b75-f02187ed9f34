/**
 * 打印机状态码配置文件
 * 包含所有打印机相关的状态码和错误信息
 * 支持原有SUPVANAPIT50PRO和精臣B1打印机错误码
 */

const printerStatusConfig = {
  // 状态码映射表
  statusCodeMap: {
    // 成功状态
    0: '请求成功',
    1: '请求成功',
    0xfd00: '正常', // 精臣SDK正常状态

    // 模板相关
    100: '模板信息回调',

    // 蓝牙相关错误 (101-117)
    101: '初始化蓝牙模块异常',
    102: '获取本机蓝牙适配器状态异常',
    103: '开始搜寻附近的蓝牙外围设备异常',
    104: '蓝牙寻找到新设备的事件异常',
    105: '蓝牙适配器不可用',
    106: '断开蓝牙失败异常',
    107: '蓝牙序列号为空',
    108: '文本Canvas不能为空',
    109: '连接T50,T80蓝牙异常',
    110: '连接硕方蓝牙异常',
    111: 'blemtu异常',
    112: '获取蓝牙设备服务异常',
    113: '获取蓝牙特征值异常',
    114: '获取特征值变化异常',
    115: '获取notify异常',
    116: '模板对象不能为空',
    117: '停止搜索蓝牙设备成功',

    // 图像处理相关错误 (118-124)
    118: '获取条形码对象数据异常',
    119: '生成图片失败异常',
    120: '二维码转换成图片异常',
    121: '图片下载异常',
    122: '获取rgba字模数据异常',
    123: '下载本地图片异常',
    124: '生成图片数据异常',

    // 打印相关错误 (125-135)
    125: 'T50,T80机器启动打印异常',
    126: '请关闭耗材仓盖',
    127: '耗材未装好',
    128: '请检查耗材余量',
    129: '未检测到耗材',
    130: '未识别到耗材',
    131: '耗材已用完',
    132: '打印异常终止',
    133: '色带错误',
    134: '压缩失败',
    135: '打印字模数据不能为空',

    // 精臣B1打印机错误码 (0xff00-0xffff)
    0xff00: '打印超时',
    0xff01: '打印机断开',
    0xff02: '数据错误',
    0xff03: '打印机忙碌',
    0xff04: '画布获取数据错误',
    0xff05: '设置纸张失败',
    0xff06: '查询打印纸张状态异常',
    0xff07: '盒盖打开',
    0xff08: '缺纸',
    0xff09: '电量不足',
    0xff0a: '电池异常',
    0xff0b: '手动停止',
    0xff0c: '数据错误',
    0xff0d: '温度过高',
    0xff0e: '出纸异常',
    0xff0f: '纸张错误',
    0xff10: 'RFID写入失败',
    0xff11: '出纸异常',
    0xff12: '发送EPC指令但未检测到RFID标签',
    0xff13: '检测到RFID标签但未发送EPC指令',
    0xff14: '缺碳带',
    0xff15: '打印头未锁紧',
    0xff16: '碳带已用完',
    0xff17: '碳带不匹配',
    0xff18: '标签纸安装异常',
    0xff19: '非法标签',
    0xff20: '非法碳带和标签',
    0xff21: '打印机超时',
    0xff22: '非专业碳带',
    0xfe01: 'EPC格式不对',
    0xfe00: '绘制元素超时错误',
    0xfffd: '打印机不支持',
    0xfd01: '失败'
  },

  // 状态码分类
  statusCategories: {
    // 成功状态
    success: [0, 1, 100, 117, 0xfd00],

    // 蓝牙相关错误
    bluetooth: [101, 102, 103, 104, 105, 106, 107, 109, 110, 111, 112, 113, 114, 115, 0xff01],

    // 数据处理错误
    dataProcessing: [108, 116, 118, 119, 120, 121, 122, 123, 124, 134, 135, 0xff02, 0xff04, 0xff05, 0xff06, 0xfe00, 0xfe01, 0xfd01],

    // 硬件/耗材错误
    hardware: [125, 126, 127, 128, 129, 130, 131, 132, 133, 0xff00, 0xff03, 0xff07, 0xff08, 0xff09, 0xff0a, 0xff0b, 0xff0c, 0xff0d, 0xff0e, 0xff0f, 0xff10, 0xff11, 0xff12, 0xff13, 0xff14, 0xff15, 0xff16, 0xff17, 0xff18, 0xff19, 0xff20, 0xff21, 0xff22, 0xfffd]
  },

  // 错误级别定义
  errorLevels: {
    // 信息级别（成功、通知等）
    info: [0, 1, 100, 117, 0xfd00],

    // 警告级别（可恢复的错误）
    warning: [105, 107, 108, 116, 126, 127, 128, 135, 0xff03, 0xff07, 0xff08, 0xff0b, 0xff14, 0xff15, 0xff17, 0xff18, 0xff22],

    // 错误级别（需要用户干预）
    error: [101, 102, 103, 104, 106, 109, 110, 111, 112, 113, 114, 115, 118, 119, 120, 121, 122, 123, 124, 125, 129, 130, 131, 132, 133, 134, 0xff00, 0xff01, 0xff02, 0xff04, 0xff05, 0xff06, 0xff09, 0xff0a, 0xff0c, 0xff0d, 0xff0e, 0xff0f, 0xff10, 0xff11, 0xff12, 0xff13, 0xff16, 0xff19, 0xff20, 0xff21, 0xfe00, 0xfe01, 0xfffd, 0xfd01]
  },

  // 用户友好的错误提示
  userFriendlyMessages: {
    // 蓝牙连接问题
    101: '蓝牙初始化失败，请重启应用后重试',
    102: '无法获取蓝牙状态，请检查设备蓝牙功能',
    103: '搜索设备失败，请确保打印机已开启',
    104: '发现新设备时出错，请重新搜索',
    105: '蓝牙不可用，请开启蓝牙后重试',
    106: '断开连接失败，请重启应用',
    107: '设备序列号为空，请重新连接设备',
    109: '连接打印机失败，请检查设备状态',
    110: '连接硕方设备失败，请重试',

    // 耗材相关问题
    126: '请关闭耗材仓盖后重试',
    127: '耗材未正确安装，请重新安装',
    128: '耗材余量不足，请更换新耗材',
    129: '未检测到耗材，请安装耗材',
    130: '无法识别耗材类型，请使用原装耗材',
    131: '耗材已用完，请更换新耗材',
    132: '打印过程中断，请检查设备状态',
    133: '色带错误，请检查色带安装',

    // 数据处理问题
    108: '数据处理错误，请重新生成标签',
    116: '模板数据错误，请重新选择模板',
    119: '图片生成失败，请重试',
    135: '打印数据为空，请检查标签内容',

    // 精臣B1打印机错误提示
    0xff00: '打印超时，请检查设备连接',
    0xff01: '打印机连接断开，请重新连接',
    0xff02: '数据错误，请重新生成标签',
    0xff03: '打印机忙碌，请稍后重试',
    0xff04: '获取画布数据失败，请重新生成',
    0xff05: '设置纸张失败，请检查纸张规格',
    0xff06: '查询打印状态异常，请重启设备',
    0xff07: '请关闭打印机盒盖',
    0xff08: '纸张用完，请安装新纸张',
    0xff09: '电量不足，请给设备充电',
    0xff0a: '电池异常，请检查电池状态',
    0xff0b: '打印已手动停止',
    0xff0c: '数据错误，请重新生成标签',
    0xff0d: '设备温度过高，请等待降温',
    0xff0e: '出纸异常，请检查纸张路径',
    0xff0f: '纸张错误，请使用正确规格纸张',
    0xff10: 'RFID写入失败，请重试',
    0xff11: '出纸异常，请检查纸张路径',
    0xff12: '未检测到RFID标签',
    0xff13: '检测到RFID标签但未发送EPC指令',
    0xff14: '缺少碳带，请安装碳带',
    0xff15: '打印头未锁紧，请检查打印头',
    0xff16: '碳带已用完，请更换新碳带',
    0xff17: '碳带不匹配，请使用正确规格碳带',
    0xff18: '标签纸安装异常，请重新安装',
    0xff19: '非法标签，请使用正确标签',
    0xff20: '非法碳带和标签，请更换耗材',
    0xff21: '打印机响应超时，请重启设备',
    0xff22: '非专业碳带，请使用原装碳带',
    0xfe01: 'EPC格式错误，请检查EPC数据',
    0xfe00: '绘制元素超时，请简化标签内容',
    0xfffd: '打印机不支持此功能',
    0xfd01: '操作失败，请重试'
  }
};

/**
 * 获取状态码对应的消息
 * @param {number} code 状态码
 * @param {boolean} userFriendly 是否返回用户友好的消息
 * @returns {string} 状态消息
 */
function getStatusMessage(code, userFriendly = false) {
  if (userFriendly && printerStatusConfig.userFriendlyMessages[code]) {
    return printerStatusConfig.userFriendlyMessages[code];
  }
  return printerStatusConfig.statusCodeMap[code] || `未知状态码: ${code}`;
}

/**
 * 获取状态码的错误级别
 * @param {number} code 状态码
 * @returns {string} 错误级别: info, warning, error
 */
function getErrorLevel(code) {
  const levels = printerStatusConfig.errorLevels;
  if (levels.info.includes(code)) return 'info';
  if (levels.warning.includes(code)) return 'warning';
  if (levels.error.includes(code)) return 'error';
  return 'unknown';
}

/**
 * 获取状态码的分类
 * @param {number} code 状态码
 * @returns {string} 分类: success, bluetooth, dataProcessing, hardware
 */
function getStatusCategory(code) {
  const categories = printerStatusConfig.statusCategories;
  for (const [category, codes] of Object.entries(categories)) {
    if (codes.includes(code)) return category;
  }
  return 'unknown';
}

/**
 * 判断是否为成功状态
 * @param {number} code 状态码
 * @returns {boolean} 是否成功
 */
function isSuccessStatus(code) {
  return printerStatusConfig.statusCategories.success.includes(code);
}

module.exports = {
  printerStatusConfig,
  getStatusMessage,
  getErrorLevel,
  getStatusCategory,
  isSuccessStatus
};
